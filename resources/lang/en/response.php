<?php
return [

    'failedRegistration' => 'Failed registering, please try again.',
    'invalidCredential' => 'Invalid Credentials.',
    'invalidCredentialData' => 'Invalid email address or a phone number.',
    'logOut' => 'User is logged out',
    'unauthorised' => 'Unauthorised',
    'invalidCode' => 'Invalid Code.',
    'unableToVerify' => 'Unable to verify. Please try again later.',
    'notFoundInDataset'=>"We can't find you in our database",
    'verificationCodeSent'=>'Account Verification Code Sent',
    'checkOldPassword'=>'Check your old password.',
    'enterNonSimilarPassword'=>'Please enter a password which is not similar then current password.',
    'passwordUpdated'=>'Password updated successfully.',
    'fcmTokenUpdated'=>'Fcm token updated successfully.',
    'passwordResetCodeSent' => 'Password Reset Code Sent',
    'invalidToken' => 'Invalid token!',
    'userNotExist' => "User doesn't  exist!",
    'passwordResetSuccess' => 'Password reset successfully',
    'emailCouldNotSend' => 'Email could not be sent to this email address',
    'errorLogin' => 'Error logging in!',
    'socialLoginError' => 'Please login using facebook, linkedin or google',
    'failedAddingAddress' => 'Failed adding address.',
    'failedUpdatingAddress' => 'Failed updating address',
    'addressRemoved' => 'Address removed successfully.',
    'failedRemovingAddress' => 'Failed removing address.',
    'gotAppointment' => "You've got a new appointment",
    'appointmentCreated'=>'Appointment created successfully',
    'appointmentRemoved'=>'Appointment removed successfully.',
    'failedRemovingAppointment'=>'Failed removing appointment.',
    'appointmentApproved'=>'The appointment has been approved.',
    'failedApprovingAppointment'=>'Failed approving appointment.',
    'pendingAppointmentExist' => 'Pending appointment already exists',
    'appointmentCancelled' => 'The appointment has been cancelled.',
    'messageDisposed'=>'Message disposed successfully.',
    'messagedMarkedConsidered'=>'Message marked as considered.',
    'languageAdded' => 'Language added successfully',
    'messageDeleted' => 'Message deleted successfully',
    'messageArchived' => 'Message archived successfully',
    'messageSeen' => 'Message seen successfully',
    'messageDelivered' => 'Message delivered successfully',
    'gotProposalRequest' => "You've got a new proposal request",
    'proposalSubmitted' => 'Proposal submitted successfully.',
    'proposalCancelled' => 'Proposal cancelled successfully.',
    'proposalAccepted' => 'Your proposal has been accepted.',
    'contractStart' => 'Started contract successfully.',
    'failedCancellingProposal' => 'Failed cancelling proposal.',
    'cannotRequestProposalForThisInquiry' => 'You cannot request proposal for this inquiry.',
    'alreadyProposalSent' => 'You already have sent a proposal.',
    'contractCompleted' => 'Completed contract successfully.',
    'ticketRemoved' => 'Ticket removed successfully.',
    'failedRemovingTicket' => 'Failed removing ticket.',
    'workExperienceUpdated' => 'Work experience updated successfully.',
    'failedUpdatingWorkExperience' => 'Failed updating work experience',
    'workExperienceRemoved' => 'Work experience removed successfully.',
    'failedRemovingWorkExperience' => 'Failed removing work experience',
    'infoUpdated' => 'Info updated successfully.',
    'stateAndCountiesUpdated' => 'State and counties updated successfully.',
    'failedUpdatingDetails' => 'Failed updating details.',
    'invalidTargetLocation' => 'Invalid target location.Please select the location inside USA.',
    'questionDeleted' => 'Question deleted successfully.',
    'failedDeletingQuestion' => 'Failed deleting question.',
    'removedPaymentMethod' => 'Payment method removed successfully',
    'failedUpdatingNews' => 'Unable to update news',
    'newsDeleted' => 'News deleted successfully',
    'newsNotFound' => 'News not found',
    'attachmentDeleted' => 'Attachment deleted successfully'
];

