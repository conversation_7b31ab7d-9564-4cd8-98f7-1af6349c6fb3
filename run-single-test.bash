#!/bin/bash
# Bash script for running a single test file
# Usage: ./run-single-test.bash path/to/test/file.php

# Check if a test file was provided
if [ -z "$1" ]; then
    echo "Error: No test file specified"
    echo "Usage: ./run-single-test.bash path/to/test/file.php"
    exit 1
fi

# Set environment to testing
export APP_ENV=testing

# Clear cache
echo "Clearing cache..."
php artisan config:clear
php artisan cache:clear

# Prepare the test database
echo "Preparing test database..."

# Create the testing database if it doesn't exist
echo "Creating testing database if it doesn't exist..."
mysql -u root -e "CREATE DATABASE IF NOT EXISTS legalfiber_testing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run migrations and seed the database
php artisan migrate:fresh --env=testing
php artisan db:seed --class="Database\\Seeders\\TestDatabaseSeeder" --env=testing

# Run the specified test
echo -e "\n=== Running Test: $1 ===\n"
php artisan test --env=testing "$1"

echo -e "\n=== Test Completed ===\n"
