@echo off
REM Windows batch script for running the test seeder

REM Set environment to testing
set APP_ENV=testing

REM Clear cache
echo Clearing cache...
php artisan config:clear
php artisan cache:clear

REM Create the testing database if it doesn't exist
echo Creating testing database if it doesn't exist...
mysql -u root -e "CREATE DATABASE IF NOT EXISTS legalfiber_testing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

REM Run migrations
echo Running migrations...
php artisan migrate:fresh --env=testing

REM Run the seeder
echo Running seeder...
php artisan db:seed --class="Database\Seeders\TestDatabaseSeeder" --env=testing

echo.
echo === Seeder Completed ===
echo.

pause
