# Troubleshooting Test Issues

This guide addresses common issues that may occur when running tests.

## "Specified key was too long" Error

If you encounter the error:

```
SQLSTATE[42000]: Syntax error or access violation: 1071 Specified key was too long; max key length is 1000 bytes
```

This is due to MySQL's limitation on index key length, especially in older versions (before MySQL 5.7.7 or MariaDB 10.2.2).

### Solution 1: Verify AppServiceProvider Configuration

Make sure your `AppServiceProvider.php` has the default string length set:

```php
use Illuminate\Support\Facades\Schema;

public function boot()
{
    Schema::defaultStringLength(191);
    // ...
}
```

### Solution 2: Check MySQL Version

Verify your MySQL version:

```bash
mysql --version
```

If you're using an older version, consider:
- Upgrading to MySQL 5.7.7+ or MariaDB 10.2.2+
- Or keeping the `Schema::defaultStringLength(191)` setting

### Solution 3: Modify MySQL SQL Mode

In your `config/database.php`, ensure you have the correct modes set:

```php
'mysql' => [
    // ...
    'modes' => [
        //'ONLY_FULL_GROUP_BY',
        'STRICT_TRANS_TABLES',
        'NO_ZERO_IN_DATE',
        'NO_ZERO_DATE',
        'ERROR_FOR_DIVISION_BY_ZERO',
        'NO_ENGINE_SUBSTITUTION',
    ],
    // ...
],
```

### Solution 4: Use a Different Database Engine

If you continue to have issues, consider using SQLite for testing:

```
DB_CONNECTION=sqlite
DB_DATABASE=:memory:
```

## MySQL Connection Issues

If you have trouble connecting to MySQL:

### Solution 1: Check MySQL Service

Ensure MySQL is running:

**Windows:**
```
net start mysql
```

**Linux:**
```
sudo systemctl status mysql
```

### Solution 2: Verify Credentials

Make sure your `.env.testing` file has the correct credentials:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=legalfiber_testing
DB_USERNAME=root
DB_PASSWORD=your_password
```

### Solution 3: Create the Database

Ensure the testing database exists:

```sql
CREATE DATABASE IF NOT EXISTS legalfiber_testing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### Solution 4: Grant Permissions

Make sure your MySQL user has the necessary permissions:

```sql
GRANT ALL PRIVILEGES ON legalfiber_testing.* TO 'your_username'@'localhost';
FLUSH PRIVILEGES;
```

## Spatial Data Issues

If you're using spatial data with the `Grimzy\LaravelMysqlSpatial` package:

### Solution 1: Use MySQL 5.7+

Spatial functions work best with MySQL 5.7 or later.

### Solution 2: Check MySQL Configuration

Ensure your MySQL server has spatial support enabled:

```sql
SHOW ENGINES;
```

InnoDB should be available and support spatial indexes.

### Solution 3: Use the Correct Storage Engine

Make sure your tables use InnoDB:

```php
Schema::create('counties', function (Blueprint $table) {
    $table->engine = 'InnoDB';
    // ...
});
```

## Other Common Issues

### PDO Extension

Make sure the PDO MySQL extension is enabled in your PHP configuration:

**Windows (php.ini):**
```
extension=pdo_mysql
```

**Linux:**
```bash
sudo apt-get install php-mysql  # For Debian/Ubuntu
sudo yum install php-mysql      # For CentOS/RHEL
```

### Memory Limits

If you encounter memory limit issues:

```
php -d memory_limit=-1 artisan test
```

### Timeouts

For long-running tests, you might need to increase the timeout:

```php
// In your TestCase.php or specific test file
protected function setUp(): void
{
    parent::setUp();
    $this->withoutExceptionHandling();
    ini_set('max_execution_time', 300); // 5 minutes
}
```

## Seeder Issues

### "Target class does not exist" Error

If you encounter the error:

```
Target class [Database\Seeders\DatabaseSeedersTestDatabaseSeeder] does not exist.
```

This is due to Laravel not being able to find the seeder class.

### Solution 1: Use Quotes Around the Class Name

When running the seeder from the command line, use quotes around the class name:

```bash
php artisan db:seed --class="Database\Seeders\TestDatabaseSeeder"
```

### Solution 2: Check Namespace and Class Name

Make sure the namespace and class name in your seeder file match what you're using in the command:

```php
namespace Database\Seeders;

class TestDatabaseSeeder extends Seeder
{
    // ...
}
```

### Solution 3: Clear Composer Autoload Cache

Run the following command to regenerate the autoload files:

```bash
composer dump-autoload
```

### Solution 4: Check for Typos

Double-check for typos in the class name or namespace. The correct class name is `TestDatabaseSeeder` (not `TestDatabaseSeeders` or `DatabaseSeedersTestDatabaseSeeder`).

### Solution 5: Run the Seeder Directly

Use the provided script to run the seeder directly:

**Windows:**
```bash
run-seeder.bat
```

**Linux:**
```bash
chmod +x run-seeder.bash
./run-seeder.bash
```
