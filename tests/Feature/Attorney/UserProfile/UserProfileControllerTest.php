<?php

namespace Tests\Feature\Attorney\UserProfile;

use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\Feature\BaseControllerTestCase;

class UserProfileControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * Test index method returns the attorney's profile.
     *
     * @return void
     */
    public function test_index_returns_attorney_profile()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/profile');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'id' => $this->attorneyUser->id,
            'name' => $this->attorneyUser->name,
            'email' => $this->attorneyUser->email
        ]);
    }

    /**
     * Test update method updates the attorney's profile.
     *
     * @return void
     */
    public function test_update_modifies_attorney_profile()
    {
        $updateData = [
            'name' => $this->faker->name,
            'phone' => $this->faker->phoneNumber,
            'phone_code' => '+1',
            'bio' => $this->faker->paragraph
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/profile/update', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('users', [
            'id' => $this->attorneyUser->id,
            'name' => $updateData['name'],
            'phone' => $updateData['phone'],
            'phone_code' => $updateData['phone_code'],
            'bio' => $updateData['bio']
        ]);
    }

    /**
     * Test update_avatar method updates the attorney's avatar.
     *
     * @return void
     */
    public function test_update_avatar_changes_attorney_profile_picture()
    {
        Storage::fake('s3');
        
        $file = UploadedFile::fake()->image('avatar.jpg');

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/profile/update-avatar', [
                'avatar' => $file
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        // The avatar URL should be updated in the database
        $this->assertNotEquals(
            $this->attorneyUser->avatar,
            $this->attorneyUser->fresh()->avatar
        );
    }

    /**
     * Test change_password method updates the attorney's password.
     *
     * @return void
     */
    public function test_change_password_updates_attorney_password()
    {
        $passwordData = [
            'current_password' => 'password', // Default password from factory
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!'
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/profile/change-password', $passwordData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        // Verify we can login with the new password
        $loginResponse = $this->withHeaders($this->headers)
            ->postJson('/api/v1/login', [
                'email' => $this->attorneyUser->email,
                'password' => 'NewPassword123!'
            ]);

        $loginResponse->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'user',
                'token'
            ]);
    }
}
