<?php

namespace Tests\Feature\Attorney;

use App\Models\PracticeArea;
use App\Models\AttorneyHasLegalPractice;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class PracticeAreaControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $practiceAreas = [];
    protected $attorneyPracticeArea;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test practice areas
        for ($i = 0; $i < 5; $i++) {
            $this->practiceAreas[] = PracticeArea::factory()->create();
        }
        
        // Associate practice area with attorney
        $this->attorneyPracticeArea = AttorneyHasLegalPractice::factory()->create([
            'user_id' => $this->attorneyUser->id,
            'practice_area_id' => $this->practiceAreas[0]->id
        ]);
    }

    /**
     * Test index method returns all practice areas.
     *
     * @return void
     */
    public function test_index_returns_all_practice_areas()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/practice-areas');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        // Verify all practice areas are returned
        foreach ($this->practiceAreas as $practiceArea) {
            $response->assertJsonFragment([
                'id' => $practiceArea->id,
                'name' => $practiceArea->name
            ]);
        }
    }

    /**
     * Test user_practice_areas method returns attorney's practice areas.
     *
     * @return void
     */
    public function test_user_practice_areas_returns_attorney_practice_areas()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/user-practice-areas');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'practice_area_id' => $this->practiceAreas[0]->id
        ]);
    }

    /**
     * Test store method adds practice areas to attorney.
     *
     * @return void
     */
    public function test_store_adds_practice_areas_to_attorney()
    {
        // Use practice areas that aren't already associated with the attorney
        $practiceAreaIds = [
            $this->practiceAreas[1]->id,
            $this->practiceAreas[2]->id
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/practice-areas/store', [
                'practice_area_id' => $practiceAreaIds
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        // Verify each practice area was added
        foreach ($practiceAreaIds as $practiceAreaId) {
            $this->assertDatabaseHas('attorney_has_legal_practice', [
                'user_id' => $this->attorneyUser->id,
                'practice_area_id' => $practiceAreaId
            ]);
        }
    }

    /**
     * Test destroy method removes a practice area from attorney.
     *
     * @return void
     */
    public function test_destroy_removes_practice_area_from_attorney()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->deleteJson("/api/v1/attorney/practice-areas/delete/{$this->attorneyPracticeArea->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseMissing('attorney_has_legal_practice', [
            'id' => $this->attorneyPracticeArea->id
        ]);
    }
}
