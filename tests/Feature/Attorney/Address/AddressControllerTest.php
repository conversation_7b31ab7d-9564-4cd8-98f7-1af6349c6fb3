<?php

namespace Tests\Feature\Attorney\Address;

use App\Models\Address;
use App\Models\Country;
use App\Models\State;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class AddressControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $country;
    protected $state;
    protected $address;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->country = Country::factory()->create();
        $this->state = State::factory()->create();
        
        // Create address for attorney
        $this->address = Address::factory()->create([
            'user_id' => $this->attorneyUser->id,
            'country_id' => $this->country->id,
            'state_id' => $this->state->id
        ]);
    }

    /**
     * Test index method returns the attorney's address.
     *
     * @return void
     */
    public function test_index_returns_attorney_address()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/address');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'id' => $this->address->id,
            'user_id' => $this->attorneyUser->id
        ]);
    }

    /**
     * Test store method creates a new address.
     *
     * @return void
     */
    public function test_store_creates_new_address()
    {
        // Delete existing address to test creation
        $this->address->delete();
        
        $addressData = [
            'address_line_1' => $this->faker->streetAddress,
            'address_line_2' => $this->faker->secondaryAddress,
            'city' => $this->faker->city,
            'state_id' => $this->state->id,
            'country_id' => $this->country->id,
            'zip_code' => $this->faker->postcode,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/address/store', $addressData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('addresses', [
            'user_id' => $this->attorneyUser->id,
            'address_line_1' => $addressData['address_line_1'],
            'city' => $addressData['city'],
            'state_id' => $addressData['state_id'],
            'country_id' => $addressData['country_id']
        ]);
    }

    /**
     * Test update method updates an existing address.
     *
     * @return void
     */
    public function test_update_modifies_existing_address()
    {
        $updateData = [
            'address_line_1' => $this->faker->streetAddress,
            'address_line_2' => $this->faker->secondaryAddress,
            'city' => $this->faker->city,
            'state_id' => $this->state->id,
            'country_id' => $this->country->id,
            'zip_code' => $this->faker->postcode,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/address/update', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('addresses', [
            'id' => $this->address->id,
            'user_id' => $this->attorneyUser->id,
            'address_line_1' => $updateData['address_line_1'],
            'city' => $updateData['city']
        ]);
    }
}
