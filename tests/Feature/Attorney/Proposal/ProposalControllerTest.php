<?php

namespace Tests\Feature\Attorney\Proposal;

use App\Models\County;
use App\Models\Inquiry;
use App\Models\Proposal;
use App\Models\State;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class ProposalControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $state;
    protected $county;
    protected $inquiry;
    protected $proposal;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->state = State::factory()->create();
        $this->county = County::factory()->create(['state_id' => $this->state->id]);
        
        // Associate attorney with county
        $this->attorneyUser->attorneyState()->create([
            'state_id' => $this->state->id,
            'county_id' => $this->county->id
        ]);
        
        // Create inquiry
        $this->inquiry = Inquiry::factory()->create([
            'user_id' => $this->clientUser->id,
            'state_id' => $this->state->id,
            'county_id' => $this->county->id
        ]);
        
        // Create proposal
        $this->proposal = Proposal::factory()->create([
            'user_id' => $this->attorneyUser->id,
            'inquiry_id' => $this->inquiry->id,
            'status' => Proposal::PENDING
        ]);
    }

    /**
     * Test index method returns paginated proposals.
     *
     * @return void
     */
    public function test_index_returns_paginated_proposals()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/proposals');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'path',
                'per_page',
                'total_records',
                'total_pages',
                'current_page',
                'record_range',
                'next',
                'previous',
                'first',
                'last',
                'records',
            ]);
    }

    /**
     * Test get method returns a specific proposal.
     *
     * @return void
     */
    public function test_get_returns_specific_proposal()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson("/api/v1/attorney/proposals/{$this->inquiry->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
    }

    /**
     * Test store method creates a new proposal.
     *
     * @return void
     */
    public function test_store_creates_new_proposal()
    {
        // Create a new inquiry for this test
        $inquiry = Inquiry::factory()->create([
            'user_id' => $this->clientUser->id,
            'state_id' => $this->state->id,
            'county_id' => $this->county->id
        ]);
        
        $proposalData = [
            'inquiry_id' => $inquiry->id,
            'message' => $this->faker->paragraph,
            'amount' => $this->faker->randomFloat(2, 100, 1000),
            'currency' => 'USD'
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/proposals/store', $proposalData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('proposals', [
            'user_id' => $this->attorneyUser->id,
            'inquiry_id' => $inquiry->id,
            'message' => $proposalData['message'],
            'amount' => $proposalData['amount'],
            'currency' => $proposalData['currency']
        ]);
    }

    /**
     * Test update method updates an existing proposal.
     *
     * @return void
     */
    public function test_update_modifies_existing_proposal()
    {
        $updateData = [
            'id' => $this->proposal->id,
            'message' => $this->faker->paragraph,
            'amount' => $this->faker->randomFloat(2, 100, 1000),
            'currency' => 'EUR'
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/proposals/update', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('proposals', [
            'id' => $this->proposal->id,
            'message' => $updateData['message'],
            'amount' => $updateData['amount'],
            'currency' => $updateData['currency']
        ]);
    }

    /**
     * Test cancel method cancels a proposal.
     *
     * @return void
     */
    public function test_cancel_proposal()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/proposals/cancel', [
                'id' => $this->proposal->id
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseHas('proposals', [
            'id' => $this->proposal->id,
            'status' => Proposal::CANCELLED
        ]);
    }
}
