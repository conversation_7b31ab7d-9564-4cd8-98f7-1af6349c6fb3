<?php

namespace Tests\Feature\Attorney\Rating;

use App\Models\Rating;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class RatingControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $rating;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create rating for attorney
        $this->rating = Rating::factory()->create([
            'attorney_id' => $this->attorneyUser->id,
            'client_id' => $this->clientUser->id,
            'rating' => 4,
            'comment' => $this->faker->paragraph
        ]);
    }

    /**
     * Test index method returns all ratings for the attorney.
     *
     * @return void
     */
    public function test_index_returns_all_attorney_ratings()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/ratings');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'id' => $this->rating->id,
            'attorney_id' => $this->attorneyUser->id,
            'client_id' => $this->clientUser->id,
            'rating' => 4
        ]);
    }

    /**
     * Test show method returns a specific rating.
     *
     * @return void
     */
    public function test_show_returns_specific_rating()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson("/api/v1/attorney/ratings/{$this->rating->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'id' => $this->rating->id,
            'attorney_id' => $this->attorneyUser->id,
            'client_id' => $this->clientUser->id,
            'rating' => 4,
            'comment' => $this->rating->comment
        ]);
    }

    /**
     * Test average method returns the attorney's average rating.
     *
     * @return void
     */
    public function test_average_returns_attorney_average_rating()
    {
        // Create additional ratings to test average calculation
        Rating::factory()->create([
            'attorney_id' => $this->attorneyUser->id,
            'client_id' => User::factory()->create(['user_type' => 'client'])->id,
            'rating' => 5
        ]);
        
        Rating::factory()->create([
            'attorney_id' => $this->attorneyUser->id,
            'client_id' => User::factory()->create(['user_type' => 'client'])->id,
            'rating' => 3
        ]);

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/ratings/average');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data' => [
                    'average_rating',
                    'total_ratings'
                ]
            ]);
        
        // Average of 4, 5, and 3 is 4
        $response->assertJson([
            'data' => [
                'average_rating' => 4,
                'total_ratings' => 3
            ]
        ]);
    }
}
