<?php

namespace Tests\Feature\Attorney\Country;

use App\Models\Country;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class CountryControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $countries = [];

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test countries
        for ($i = 0; $i < 5; $i++) {
            $this->countries[] = Country::factory()->create([
                'sorting' => $i
            ]);
        }
    }

    /**
     * Test index method returns all countries.
     *
     * @return void
     */
    public function test_index_returns_all_countries()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/countries');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        // Verify all countries are returned
        foreach ($this->countries as $country) {
            $response->assertJsonFragment([
                'id' => $country->id,
                'name' => $country->name
            ]);
        }
    }
}
