<?php

namespace Tests\Feature\Attorney;

use App\Models\State;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class StateControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $states = [];

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test states
        for ($i = 0; $i < 5; $i++) {
            $this->states[] = State::factory()->create();
        }
    }

    /**
     * Test index method returns all states.
     *
     * @return void
     */
    public function test_index_returns_all_states()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/states');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        // Verify all states are returned
        foreach ($this->states as $state) {
            $response->assertJsonFragment([
                'id' => $state->id,
                'name' => $state->name,
                'abbr' => $state->abbr
            ]);
        }
    }
}
