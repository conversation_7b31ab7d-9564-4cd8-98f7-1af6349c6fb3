<?php

namespace Tests\Feature\Attorney\ClientInquiry;

use App\Models\County;
use App\Models\Inquiry;
use App\Models\State;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class ClientInquiryControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $state;
    protected $county;
    protected $inquiries = [];

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->state = State::factory()->create();
        $this->county = County::factory()->create(['state_id' => $this->state->id]);
        
        // Associate attorney with county
        $this->attorneyUser->attorneyState()->create([
            'state_id' => $this->state->id,
            'county_id' => $this->county->id
        ]);
        
        // Create inquiries
        for ($i = 0; $i < 5; $i++) {
            $client = User::factory()->create(['user_type' => 'client']);
            $inquiry = Inquiry::factory()->create([
                'user_id' => $client->id,
                'state_id' => $this->state->id,
                'county_id' => $this->county->id
            ]);
            $this->inquiries[] = $inquiry;
        }
    }

    /**
     * Test index method returns paginated inquiries.
     *
     * @return void
     */
    public function test_index_returns_paginated_inquiries()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/client-inquiries');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'path',
                'per_page',
                'total_records',
                'total_pages',
                'current_page',
                'record_range',
                'next',
                'previous',
                'first',
                'last',
                'records',
            ]);
    }

    /**
     * Test get method returns a specific inquiry.
     *
     * @return void
     */
    public function test_get_returns_specific_inquiry()
    {
        $inquiry = $this->inquiries[0];
        
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson("/api/v1/attorney/client-inquiries/{$inquiry->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data' => [
                    'id',
                    'title',
                    'message',
                    'user_id',
                    'created_at',
                    'practice_areas',
                    'client',
                ]
            ]);
    }

    /**
     * Test dispensation method marks inquiry as disposed.
     *
     * @return void
     */
    public function test_dispensation_marks_inquiry_as_disposed()
    {
        $inquiry = $this->inquiries[0];
        
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson("/api/v1/attorney/client-inquiries/dispensation", [
                'inquiry_id' => $inquiry->id,
                'status' => '2'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);
    }

    /**
     * Test dispensation method marks inquiry as considered.
     *
     * @return void
     */
    public function test_dispensation_marks_inquiry_as_considered()
    {
        $inquiry = $this->inquiries[0];
        
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson("/api/v1/attorney/client-inquiries/dispensation", [
                'inquiry_id' => $inquiry->id,
                'status' => '3'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data',
                'message'
            ]);
    }
}
