<?php

namespace Tests\Feature\Attorney\Appointment;

use App\Models\Appointment;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class AppointmentControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $appointment;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create appointment
        $this->appointment = Appointment::factory()->create([
            'attorney_id' => $this->attorneyUser->id,
            'client_id' => $this->clientUser->id,
            'time' => Carbon::now()->addDays(2)->format('Y-m-d H:i'),
            'status' => 'pending'
        ]);
    }

    /**
     * Test index method returns all appointments.
     *
     * @return void
     */
    public function test_index_returns_all_appointments()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/appointments');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'id' => $this->appointment->id,
            'attorney_id' => $this->attorneyUser->id,
            'client_id' => $this->clientUser->id
        ]);
    }

    /**
     * Test store method creates a new appointment.
     *
     * @return void
     */
    public function test_store_creates_new_appointment()
    {
        $appointmentData = [
            'client_id' => $this->clientUser->id,
            'time' => Carbon::now()->addDays(5)->format('Y-m-d H:i'),
            'duration' => 60,
            'title' => $this->faker->sentence,
            'description' => $this->faker->paragraph,
            'meeting_type' => 'video'
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/appointments/store', $appointmentData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('appointments', [
            'attorney_id' => $this->attorneyUser->id,
            'client_id' => $this->clientUser->id,
            'title' => $appointmentData['title'],
            'meeting_type' => $appointmentData['meeting_type']
        ]);
    }

    /**
     * Test update method updates an existing appointment.
     *
     * @return void
     */
    public function test_update_modifies_existing_appointment()
    {
        $updateData = [
            'id' => $this->appointment->id,
            'time' => Carbon::now()->addDays(7)->format('Y-m-d H:i'),
            'duration' => 90,
            'title' => $this->faker->sentence,
            'description' => $this->faker->paragraph,
            'meeting_type' => 'in-person'
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/appointments/update', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('appointments', [
            'id' => $this->appointment->id,
            'title' => $updateData['title'],
            'meeting_type' => $updateData['meeting_type'],
            'duration' => $updateData['duration']
        ]);
    }

    /**
     * Test cancel method cancels an appointment.
     *
     * @return void
     */
    public function test_cancel_appointment()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/appointments/cancel', [
                'id' => $this->appointment->id
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseHas('appointments', [
            'id' => $this->appointment->id,
            'status' => 'cancelled'
        ]);
    }

    /**
     * Test complete method marks an appointment as completed.
     *
     * @return void
     */
    public function test_complete_appointment()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/appointments/complete', [
                'id' => $this->appointment->id
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseHas('appointments', [
            'id' => $this->appointment->id,
            'status' => 'completed'
        ]);
    }
}
