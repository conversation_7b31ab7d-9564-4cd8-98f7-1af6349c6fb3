<?php

namespace Tests\Feature\Attorney\Support;

use App\Models\Support;
use App\Models\SupportCategory;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\Feature\BaseControllerTestCase;

class SupportControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $supportCategory;
    protected $activeTicket;
    protected $closedTicket;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->supportCategory = SupportCategory::factory()->create();
        
        // Create active ticket
        $this->activeTicket = Support::factory()->create([
            'user_id' => $this->attorneyUser->id,
            'support_category_id' => $this->supportCategory->id,
            'is_resolved' => false
        ]);
        
        // Create closed ticket
        $this->closedTicket = Support::factory()->create([
            'user_id' => $this->attorneyUser->id,
            'support_category_id' => $this->supportCategory->id,
            'is_resolved' => true
        ]);
    }

    /**
     * Test index method returns all support tickets.
     *
     * @return void
     */
    public function test_index_returns_all_support_tickets()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/support');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data' => [
                    'active_tickets',
                    'closed_tickets'
                ]
            ]);
    }

    /**
     * Test category method returns all support categories.
     *
     * @return void
     */
    public function test_category_returns_all_support_categories()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/support/category');

        $response->assertStatus(200);
    }

    /**
     * Test store method creates a new support ticket.
     *
     * @return void
     */
    public function test_store_creates_new_support_ticket()
    {
        Storage::fake('s3');
        
        $ticketData = [
            'title' => $this->faker->sentence,
            'description' => $this->faker->paragraph,
            'support_category_id' => $this->supportCategory->id,
            'support_document' => [
                UploadedFile::fake()->create('document.pdf', 100)
            ]
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/support/store', $ticketData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('supports', [
            'user_id' => $this->attorneyUser->id,
            'title' => $ticketData['title'],
            'description' => $ticketData['description'],
            'support_category_id' => $ticketData['support_category_id']
        ]);
    }

    /**
     * Test show method returns a specific support ticket.
     *
     * @return void
     */
    public function test_show_returns_specific_support_ticket()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson("/api/v1/attorney/support/{$this->activeTicket->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
    }

    /**
     * Test update method marks a ticket as resolved.
     *
     * @return void
     */
    public function test_update_marks_ticket_as_resolved()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson("/api/v1/attorney/support/update", [
                'id' => $this->activeTicket->id,
                'is_resolved' => true
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseHas('supports', [
            'id' => $this->activeTicket->id,
            'is_resolved' => true
        ]);
    }
}
