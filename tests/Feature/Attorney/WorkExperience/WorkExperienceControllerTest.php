<?php

namespace Tests\Feature\Attorney\WorkExperience;

use App\Models\WorkExperience;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class WorkExperienceControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $workExperience;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create work experience for attorney
        $this->workExperience = WorkExperience::factory()->create([
            'user_id' => $this->attorneyUser->id
        ]);
    }

    /**
     * Test index method returns all work experiences.
     *
     * @return void
     */
    public function test_index_returns_all_work_experiences()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/work-experience');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'id' => $this->workExperience->id,
            'user_id' => $this->attorneyUser->id
        ]);
    }

    /**
     * Test store method creates a new work experience.
     *
     * @return void
     */
    public function test_store_creates_new_work_experience()
    {
        $workExperienceData = [
            'company_name' => $this->faker->company,
            'job_title' => $this->faker->jobTitle,
            'start_date' => $this->faker->date(),
            'end_date' => $this->faker->date(),
            'is_current' => false,
            'description' => $this->faker->paragraph
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/work-experience/store', $workExperienceData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('work_experiences', [
            'user_id' => $this->attorneyUser->id,
            'company_name' => $workExperienceData['company_name'],
            'job_title' => $workExperienceData['job_title']
        ]);
    }

    /**
     * Test update method updates an existing work experience.
     *
     * @return void
     */
    public function test_update_modifies_existing_work_experience()
    {
        $updateData = [
            'id' => $this->workExperience->id,
            'company_name' => $this->faker->company,
            'job_title' => $this->faker->jobTitle,
            'start_date' => $this->faker->date(),
            'end_date' => $this->faker->date(),
            'is_current' => true,
            'description' => $this->faker->paragraph
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/work-experience/update', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('work_experiences', [
            'id' => $this->workExperience->id,
            'company_name' => $updateData['company_name'],
            'job_title' => $updateData['job_title'],
            'is_current' => $updateData['is_current']
        ]);
    }

    /**
     * Test destroy method deletes a work experience.
     *
     * @return void
     */
    public function test_destroy_deletes_work_experience()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->deleteJson("/api/v1/attorney/work-experience/delete/{$this->workExperience->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseMissing('work_experiences', [
            'id' => $this->workExperience->id
        ]);
    }
}
