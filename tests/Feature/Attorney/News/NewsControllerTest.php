<?php

namespace Tests\Feature\Attorney\News;

use App\Models\News;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class NewsControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $news = [];

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test news
        for ($i = 0; $i < 5; $i++) {
            $this->news[] = News::factory()->create();
        }
    }

    /**
     * Test index method returns paginated news.
     *
     * @return void
     */
    public function test_index_returns_paginated_news()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/news');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'path',
                'per_page',
                'total_records',
                'total_pages',
                'current_page',
                'record_range',
                'next',
                'previous',
                'first',
                'last',
                'records',
            ]);
    }

    /**
     * Test show method returns a specific news.
     *
     * @return void
     */
    public function test_show_returns_specific_news()
    {
        $news = $this->news[0];
        
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson("/api/v1/attorney/news/{$news->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ])
            ->assertJson([
                'data' => [
                    'id' => $news->id,
                    'title' => $news->title
                ]
            ]);
    }

    /**
     * Test show method returns error for non-existent news.
     *
     * @return void
     */
    public function test_show_returns_error_for_nonexistent_news()
    {
        $nonExistentId = 9999;
        
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson("/api/v1/attorney/news/{$nonExistentId}");

        $response->assertStatus(400)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);
    }
}
