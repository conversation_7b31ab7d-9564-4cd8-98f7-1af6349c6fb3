<?php

namespace Tests\Feature\Attorney\Language;

use App\Models\Language;
use App\Models\UserHasLanguage;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class LanguageControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $language;
    protected $userLanguage;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test language
        $this->language = Language::factory()->create();
        
        // Associate language with attorney
        $this->userLanguage = UserHasLanguage::factory()->create([
            'user_id' => $this->attorneyUser->id,
            'language_id' => $this->language->id,
            'proficiency' => 'fluent'
        ]);
    }

    /**
     * Test index method returns all languages.
     *
     * @return void
     */
    public function test_index_returns_all_languages()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/languages');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
    }

    /**
     * Test user_languages method returns attorney's languages.
     *
     * @return void
     */
    public function test_user_languages_returns_attorney_languages()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->getJson('/api/v1/attorney/user-languages');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'language_id' => $this->language->id,
            'user_id' => $this->attorneyUser->id,
            'proficiency' => 'fluent'
        ]);
    }

    /**
     * Test store method adds a language to attorney.
     *
     * @return void
     */
    public function test_store_adds_language_to_attorney()
    {
        // Create a new language for this test
        $newLanguage = Language::factory()->create();
        
        $languageData = [
            'language_id' => $newLanguage->id,
            'proficiency' => 'intermediate'
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/languages/store', $languageData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('user_has_languages', [
            'user_id' => $this->attorneyUser->id,
            'language_id' => $newLanguage->id,
            'proficiency' => 'intermediate'
        ]);
    }

    /**
     * Test update method updates attorney's language proficiency.
     *
     * @return void
     */
    public function test_update_modifies_attorney_language_proficiency()
    {
        $updateData = [
            'id' => $this->userLanguage->id,
            'language_id' => $this->language->id,
            'proficiency' => 'native'
        ];

        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->postJson('/api/v1/attorney/languages/update', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('user_has_languages', [
            'id' => $this->userLanguage->id,
            'user_id' => $this->attorneyUser->id,
            'language_id' => $this->language->id,
            'proficiency' => 'native'
        ]);
    }

    /**
     * Test destroy method removes a language from attorney.
     *
     * @return void
     */
    public function test_destroy_removes_language_from_attorney()
    {
        $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
            ->deleteJson("/api/v1/attorney/languages/delete/{$this->userLanguage->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseMissing('user_has_languages', [
            'id' => $this->userLanguage->id
        ]);
    }
}
