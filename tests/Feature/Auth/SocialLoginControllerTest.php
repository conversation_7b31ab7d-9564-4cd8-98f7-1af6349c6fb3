<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Mockery;
use Tests\Feature\BaseControllerTestCase;

class SocialLoginControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    /**
     * Test social login with existing user.
     *
     * @return void
     */
    public function test_social_login_with_existing_user()
    {
        // Create a user with the same email as the social provider will return
        $email = $this->faker->email;
        $user = User::factory()->create([
            'email' => $email,
            'user_type' => 'client'
        ]);

        // Mock the Socialite facade
        $abstractUser = Mockery::mock('Laravel\Socialite\Two\User');
        $abstractUser->shouldReceive('getId')
            ->andReturn('123456')
            ->shouldR<PERSON>eive('getEmail')
            ->andReturn($email)
            ->shouldReceive('getName')
            ->andReturn($this->faker->name);

        Socialite::shouldR<PERSON>eive('driver->userFromToken')
            ->andReturn($abstractUser);

        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/social-login', [
                'provider' => 'google',
                'token' => 'fake-token',
                'user_type' => 'client'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'user',
                'token'
            ]);

        $response->assertJson([
            'user' => [
                'id' => $user->id,
                'email' => $email
            ]
        ]);
    }

    /**
     * Test social login creating a new user.
     *
     * @return void
     */
    public function test_social_login_creates_new_user()
    {
        $email = $this->faker->email;
        $name = $this->faker->name;

        // Mock the Socialite facade
        $abstractUser = Mockery::mock('Laravel\Socialite\Two\User');
        $abstractUser->shouldReceive('getId')
            ->andReturn('123456')
            ->shouldReceive('getEmail')
            ->andReturn($email)
            ->shouldReceive('getName')
            ->andReturn($name);

        Socialite::shouldReceive('driver->userFromToken')
            ->andReturn($abstractUser);

        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/social-login', [
                'provider' => 'google',
                'token' => 'fake-token',
                'user_type' => 'client'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'user',
                'token'
            ]);

        $this->assertDatabaseHas('users', [
            'email' => $email,
            'name' => $name,
            'user_type' => 'client'
        ]);
    }
}
