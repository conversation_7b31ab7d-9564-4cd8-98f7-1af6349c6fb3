<?php

namespace Tests\Feature\Auth;

use App\Models\Code;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\Feature\BaseControllerTestCase;

class AuthControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    /**
     * Test user registration.
     *
     * @return void
     */
    public function test_register_creates_new_user()
    {
        $userData = [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone_code' => '+1'
        ];

        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/register', $userData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'user',
                'token'
            ]);

        $this->assertDatabaseHas('users', [
            'email' => $userData['email'],
            'name' => $userData['name']
        ]);
    }

    /**
     * Test user login with email.
     *
     * @return void
     */
    public function test_login_with_email_returns_token()
    {
        $user = User::factory()->create([
            'password' => Hash::make('Password123!'),
            'user_type' => 'client'
        ]);

        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/login', [
                'email' => $user->email,
                'password' => 'Password123!'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'user',
                'token'
            ]);
    }

    /**
     * Test user login with phone.
     *
     * @return void
     */
    public function test_login_with_phone_returns_token()
    {
        $user = User::factory()->create([
            'phone' => '1234567890',
            'password' => Hash::make('Password123!'),
            'user_type' => 'client'
        ]);

        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/login', [
                'email' => '1234567890',
                'password' => 'Password123!'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'user',
                'token'
            ]);
    }

    /**
     * Test user logout.
     *
     * @return void
     */
    public function test_logout_revokes_token()
    {
        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->postJson('/api/v1/logout');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);
    }

    /**
     * Test account verification.
     *
     * @return void
     */
    public function test_verify_account_with_valid_code()
    {
        $code = '123456';
        
        Code::create([
            'user_id' => $this->clientUser->id,
            'code' => $code,
            'type' => '2'
        ]);

        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/verify', [
                'code' => $code,
                'type' => '2'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseHas('users', [
            'id' => $this->clientUser->id,
            'status' => 1
        ]);
    }

    /**
     * Test sending account verification code.
     *
     * @return void
     */
    public function test_send_account_verify_code()
    {
        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/send-account-verify-code', [
                'email' => $this->clientUser->email
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseHas('codes', [
            'user_id' => $this->clientUser->id,
            'type' => '2'
        ]);
    }

    /**
     * Test updating FCM token.
     *
     * @return void
     */
    public function test_update_fcm_token()
    {
        $token = $this->faker->uuid;
        $deviceToken = $this->faker->uuid;

        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->postJson('/api/v1/update-token', [
                'token' => $token,
                'deviceToken' => $deviceToken
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseHas('users', [
            'id' => $this->clientUser->id,
            'fcm_token' => $token,
            'deviceToken' => $deviceToken
        ]);
    }

    /**
     * Test user deactivation.
     *
     * @return void
     */
    public function test_deactivate_user_account()
    {
        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->postJson('/api/v1/deactivate');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertSoftDeleted('users', [
            'id' => $this->clientUser->id
        ]);
    }
}
