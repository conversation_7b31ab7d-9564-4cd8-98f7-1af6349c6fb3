<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Auth\Notifications\ResetPassword;
use Tests\Feature\BaseControllerTestCase;

class ForgotPasswordControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    /**
     * Test sending password reset link.
     *
     * @return void
     */
    public function test_send_reset_link_email()
    {
        Notification::fake();

        $user = User::factory()->create();

        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/password/email', [
                'email' => $user->email
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        // Check that a reset token was created
        $this->assertDatabaseHas('password_resets', [
            'email' => $user->email
        ]);

        // Check that the notification was sent
        Notification::assertSentTo($user, ResetPassword::class);
    }

    /**
     * Test sending password reset link with invalid email.
     *
     * @return void
     */
    public function test_send_reset_link_email_with_invalid_email()
    {
        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/password/email', [
                'email' => '<EMAIL>'
            ]);

        $response->assertStatus(400)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);
    }
}
