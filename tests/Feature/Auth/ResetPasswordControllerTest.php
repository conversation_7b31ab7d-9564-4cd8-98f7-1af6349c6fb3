<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Tests\Feature\BaseControllerTestCase;

class ResetPasswordControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    /**
     * Test resetting password.
     *
     * @return void
     */
    public function test_reset_password()
    {
        $user = User::factory()->create();
        $token = Str::random(60);

        // Create a password reset token
        DB::table('password_resets')->insert([
            'email' => $user->email,
            'token' => Hash::make($token),
            'created_at' => now()
        ]);

        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/password/reset', [
                'email' => $user->email,
                'token' => $token,
                'password' => 'NewPassword123!',
                'password_confirmation' => 'NewPassword123!'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        // Verify the password was changed
        $this->assertTrue(Hash::check('NewPassword123!', $user->fresh()->password));

        // Verify the token was deleted
        $this->assertDatabaseMissing('password_resets', [
            'email' => $user->email
        ]);
    }

    /**
     * Test resetting password with invalid token.
     *
     * @return void
     */
    public function test_reset_password_with_invalid_token()
    {
        $user = User::factory()->create();
        
        $response = $this->withHeaders($this->headers)
            ->postJson('/api/v1/password/reset', [
                'email' => $user->email,
                'token' => 'invalid-token',
                'password' => 'NewPassword123!',
                'password_confirmation' => 'NewPassword123!'
            ]);

        $response->assertStatus(400)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        // Verify the password was not changed
        $this->assertFalse(Hash::check('NewPassword123!', $user->fresh()->password));
    }
}
