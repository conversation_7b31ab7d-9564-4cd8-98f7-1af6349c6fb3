<?php

namespace Tests\Feature\Client\Inquiry;

use App\Models\County;
use App\Models\Inquiry;
use App\Models\PracticeArea;
use App\Models\State;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\Feature\BaseControllerTestCase;

class InquiryControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $state;
    protected $county;
    protected $practiceArea;
    protected $inquiry;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->state = State::factory()->create();
        $this->county = County::factory()->create(['state_id' => $this->state->id]);
        $this->practiceArea = PracticeArea::factory()->create();
        
        // Create inquiry
        $this->inquiry = Inquiry::factory()->create([
            'user_id' => $this->clientUser->id,
            'state_id' => $this->state->id,
            'county_id' => $this->county->id
        ]);
        
        // Associate practice area with inquiry
        $this->inquiry->practice_areas()->attach($this->practiceArea->id);
    }

    /**
     * Test index method returns paginated inquiries.
     *
     * @return void
     */
    public function test_index_returns_paginated_inquiries()
    {
        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->getJson('/api/v1/client/inquiries/get');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'path',
                'per_page',
                'total_records',
                'total_pages',
                'current_page',
                'record_range',
                'next',
                'previous',
                'first',
                'last',
                'records',
            ]);
    }

    /**
     * Test show method returns a specific inquiry.
     *
     * @return void
     */
    public function test_show_returns_specific_inquiry()
    {
        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->getJson("/api/v1/client/inquiries/get/{$this->inquiry->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data' => [
                    'id',
                    'title',
                    'message',
                    'practice_areas'
                ]
            ]);
    }

    /**
     * Test store method creates a new inquiry.
     *
     * @return void
     */
    public function test_store_creates_new_inquiry()
    {
        Storage::fake('s3');
        
        $inquiryData = [
            'title' => $this->faker->sentence,
            'message' => $this->faker->paragraph,
            'practice_area_id' => [$this->practiceArea->id],
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'inquiry_document' => [
                UploadedFile::fake()->create('document.pdf', 100)
            ]
        ];

        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->postJson('/api/v1/client/inquiries/add', $inquiryData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data' => [
                    'id',
                    'title',
                    'message',
                    'practice_areas',
                    'attachments'
                ]
            ]);

        $this->assertDatabaseHas('inquiries', [
            'user_id' => $this->clientUser->id,
            'title' => $inquiryData['title'],
            'message' => $inquiryData['message']
        ]);
    }

    /**
     * Test update method updates an existing inquiry.
     *
     * @return void
     */
    public function test_update_modifies_existing_inquiry()
    {
        $updateData = [
            'title' => $this->faker->sentence,
            'message' => $this->faker->paragraph,
            'practice_area_id' => [$this->practiceArea->id],
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude
        ];

        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->postJson("/api/v1/client/inquiries/{$this->inquiry->id}/update", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('inquiries', [
            'id' => $this->inquiry->id,
            'title' => $updateData['title'],
            'message' => $updateData['message']
        ]);
    }

    /**
     * Test destroy method deletes an inquiry.
     *
     * @return void
     */
    public function test_destroy_deletes_inquiry()
    {
        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->deleteJson("/api/v1/client/inquiries/delete/{$this->inquiry->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseMissing('inquiries', [
            'id' => $this->inquiry->id
        ]);
    }
}
