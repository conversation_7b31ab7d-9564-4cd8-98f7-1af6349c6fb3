<?php

namespace Tests\Feature\Client\Message;

use App\Models\Message;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\BaseControllerTestCase;

class MessageControllerTest extends BaseControllerTestCase
{
    use WithFaker;

    protected $message;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create message between client and attorney
        $this->message = Message::factory()->create([
            'sender_id' => $this->clientUser->id,
            'receiver_id' => $this->attorneyUser->id,
            'message' => $this->faker->sentence,
            'is_seen' => false,
            'is_delivered' => true
        ]);
    }

    /**
     * Test index method returns all messages for the client.
     *
     * @return void
     */
    public function test_index_returns_all_client_messages()
    {
        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->getJson('/api/v1/client/messages');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
    }

    /**
     * Test store method creates a new message.
     *
     * @return void
     */
    public function test_store_creates_new_message()
    {
        $messageData = [
            'receiver_id' => $this->attorneyUser->id,
            'message' => $this->faker->paragraph
        ];

        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->postJson('/api/v1/client/messages/store', $messageData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);

        $this->assertDatabaseHas('messages', [
            'sender_id' => $this->clientUser->id,
            'receiver_id' => $this->attorneyUser->id,
            'message' => $messageData['message'],
            'is_delivered' => true
        ]);
    }

    /**
     * Test conversation method returns messages between client and attorney.
     *
     * @return void
     */
    public function test_conversation_returns_messages_between_users()
    {
        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->getJson("/api/v1/client/messages/conversation/{$this->attorneyUser->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'data'
            ]);
        
        $response->assertJsonFragment([
            'id' => $this->message->id,
            'sender_id' => $this->clientUser->id,
            'receiver_id' => $this->attorneyUser->id,
            'message' => $this->message->message
        ]);
    }

    /**
     * Test mark_as_seen method marks messages as seen.
     *
     * @return void
     */
    public function test_mark_as_seen_updates_message_status()
    {
        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->postJson('/api/v1/client/messages/mark-as-seen', [
                'sender_id' => $this->attorneyUser->id
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        // Create a message from attorney to client to test marking as seen
        $messageFromAttorney = Message::factory()->create([
            'sender_id' => $this->attorneyUser->id,
            'receiver_id' => $this->clientUser->id,
            'message' => $this->faker->sentence,
            'is_seen' => false
        ]);

        $this->assertDatabaseHas('messages', [
            'id' => $messageFromAttorney->id,
            'is_seen' => true
        ]);
    }

    /**
     * Test mark_as_delivered method marks messages as delivered.
     *
     * @return void
     */
    public function test_mark_as_delivered_updates_message_status()
    {
        // Create a message with is_delivered = false
        $undeliveredMessage = Message::factory()->create([
            'sender_id' => $this->attorneyUser->id,
            'receiver_id' => $this->clientUser->id,
            'message' => $this->faker->sentence,
            'is_delivered' => false
        ]);

        $response = $this->withHeaders($this->authHeaders($this->clientUser))
            ->postJson('/api/v1/client/messages/mark-as-delivered', [
                'message_id' => $undeliveredMessage->id
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status_code',
                'message'
            ]);

        $this->assertDatabaseHas('messages', [
            'id' => $undeliveredMessage->id,
            'is_delivered' => true
        ]);
    }
}
