<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Tests\Traits\TestHelpers;

abstract class BaseControllerTestCase extends TestCase
{
    use RefreshDatabase;
    use TestHelpers;

    protected $clientUser;
    protected $attorneyUser;
    protected $adminUser;
    protected $headers = [];

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Set up the test environment
        $this->setUpTestEnvironment();

        // Get test users
        $this->clientUser = $this->getClientUser();
        $this->attorneyUser = $this->getAttorneyUser();
        $this->adminUser = $this->getAdminUser();

        // Set up headers for API requests
        $this->headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * Get authentication token for a user
     *
     * @param User $user
     * @return string
     */
    protected function getAuthToken(User $user): string
    {
        $token = $user->createToken(env('ACCESS_TOKEN'))->accessToken;
        return $token;
    }

    /**
     * Set authentication headers for a user
     *
     * @param User $user
     * @return array
     */
    protected function authHeaders(User $user): array
    {
        $token = $this->getAuthToken($user);
        return array_merge($this->headers, [
            'Authorization' => 'Bearer ' . $token,
        ]);
    }
}
