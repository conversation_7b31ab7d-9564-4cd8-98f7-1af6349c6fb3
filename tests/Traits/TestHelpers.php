<?php

namespace Tests\Traits;

use App\Models\User;
use Illuminate\Support\Facades\Artisan;
use Lara<PERSON>\Passport\Passport;

trait TestHelpers
{
    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUpTestEnvironment()
    {
        // Run migrations
        Artisan::call('migrate:fresh');
        
        // Seed the database with test data
        Artisan::call('db:seed', ['--class' => 'Database\\Seeders\\TestDatabaseSeeder']);
    }

    /**
     * Act as a specific user.
     *
     * @param User $user
     * @return void
     */
    protected function actingAs(User $user)
    {
        Passport::actingAs($user);
        return $this;
    }

    /**
     * Get a client user.
     *
     * @return User
     */
    protected function getClientUser()
    {
        return User::where('email', '<EMAIL>')->first();
    }

    /**
     * Get an attorney user.
     *
     * @return User
     */
    protected function getAttorneyUser()
    {
        return User::where('email', '<EMAIL>')->first();
    }

    /**
     * Get an admin user.
     *
     * @return User
     */
    protected function getAdminUser()
    {
        return User::where('email', '<EMAIL>')->first();
    }

    /**
     * Get authentication headers for a user.
     *
     * @param User $user
     * @return array
     */
    protected function getAuthHeaders(User $user)
    {
        $token = $user->createToken(env('ACCESS_TOKEN'))->accessToken;
        
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
        ];
    }
}
