# Controller Test Suite

This directory contains a comprehensive test suite for all controllers in the LegalFiber API application.

## Test Structure

The tests are organized as follows:

- `Feature/` - Contains all feature tests, including controller tests
  - `Auth/` - Tests for authentication controllers
  - `Attorney/` - Tests for attorney-specific controllers
  - `Client/` - Tests for client-specific controllers
  - `Admin/` - Tests for admin-specific controllers
  - `BaseControllerTestCase.php` - Base class for all controller tests
- `Traits/` - Contains helper traits for testing
  - `TestHelpers.php` - Helper methods for setting up the test environment

## Setup

Before running the tests, make sure you have set up the test environment:

1. Copy the `.env.testing` file to your project root if it doesn't exist already
2. Generate an app key for testing if needed:
   ```bash
   php artisan key:generate --env=testing
   ```
3. Configure your database for testing in `.env.testing`:

   You have three options for the database:

   **Option 1: SQLite in-memory database (default)**
   ```
   DB_CONNECTION=sqlite
   DB_DATABASE=:memory:
   ```
   This is the fastest option and requires no setup, but it doesn't exactly match your production environment.

   **Option 2: SQLite file database**
   ```
   DB_CONNECTION=sqlite
   DB_DATABASE=D:/path/to/your/project/database/testing.sqlite  # Windows path
   # or
   DB_DATABASE=/path/to/your/project/database/testing.sqlite    # Linux path
   ```
   Make sure to create the empty file first. This is useful if you have issues with the in-memory database.

   **Option 3: MySQL/MariaDB**
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=legalfiber_testing
   DB_USERNAME=root
   DB_PASSWORD=your_password
   ```
   This matches your production environment but requires you to create a separate testing database.

   To create the testing database:
   ```sql
   CREATE DATABASE legalfiber_testing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

## Running Tests

You can run the tests using the following commands:

```bash
# Run all tests
php artisan test --env=testing

# Run all feature tests
php artisan test --testsuite=Feature --env=testing

# Run specific test file
php artisan test tests/Feature/Attorney/ClientInquiry/ClientInquiryControllerTest.php --env=testing

# Run tests with specific filter
php artisan test --filter=test_index_returns_paginated_inquiries --env=testing
```

Alternatively, you can use the provided scripts which handle environment setup and test execution:

**For Windows:**
```bash
# Run all tests
run-controller-tests.bat

# Run a single test file
run-single-test.bat tests/Feature/Auth/AuthControllerTest.php
```

**For Linux/Mac:**
```bash
# Make the scripts executable
chmod +x run-controller-tests.bash
chmod +x run-single-test.bash

# Run all tests
./run-controller-tests.bash

# Run a single test file
./run-single-test.bash tests/Feature/Auth/AuthControllerTest.php
```

## Test Coverage

The test suite covers the following controllers:

### Auth Controllers
- AuthController
- ForgotPasswordController
- ResetPasswordController
- SocialLoginController

### Attorney Controllers
- AddressController
- AppointmentController
- AudioVideoController
- ClientInquiryController
- CountiesController
- CountryController
- LanguageController
- NewsController
- PracticeAreaController
- ProposalController
- RatingController
- StateController
- SupportController
- UserProfileController
- WorkExperienceController

### Client Controllers
- AddressController
- AppointmentController
- AudioVideoController
- CountryController
- InquiryController
- LanguageController
- MessageController
- NewsController
- PracticeAreaController
- ProfileController
- ProposalController
- RatingController
- StateController
- SupportController

## Test Data

The tests use factories to generate test data. The factories are located in the `database/factories` directory. The test data is seeded using the `TestDatabaseSeeder` class.

## Model Factories

The following model factories are available for testing:

- `UserFactory` - Creates users with different roles (client, attorney, admin)
- `StateFactory` - Creates states
- `CountryFactory` - Creates countries
- `CountyFactory` - Creates counties
- `AddressFactory` - Creates addresses
- `InquiryFactory` - Creates inquiries
- `PracticeAreaFactory` - Creates practice areas
- `AttorneyHasLegalPracticeFactory` - Associates attorneys with practice areas
- `AttorneyHasStatesFactory` - Associates attorneys with states
- `ProposalFactory` - Creates proposals
- `WorkExperienceFactory` - Creates work experiences
- `LanguageFactory` - Creates languages
- `UserLanguageFactory` - Associates users with languages
- `AppointmentFactory` - Creates appointments
- `RatingFactory` - Creates ratings
- `MessageFactory` - Creates messages
- `NewsFactory` - Creates news
- `SupportCategoryFactory` - Creates support categories
- `SupportFactory` - Creates support tickets
- `CodeFactory` - Creates verification codes

## Adding New Tests

When adding new tests, follow these guidelines:

1. Extend the `BaseControllerTestCase` class
2. Use the `WithFaker` trait for generating test data
3. Set up test data in the `setUp` method
4. Create test methods for each endpoint/action
5. Use descriptive test method names (e.g., `test_index_returns_paginated_inquiries`)
6. Use assertions to verify the response structure and content
7. Clean up test data in the `tearDown` method if necessary

## Example Test

```php
public function test_index_returns_paginated_inquiries()
{
    $response = $this->withHeaders($this->authHeaders($this->attorneyUser))
        ->getJson('/api/v1/attorney/client-inquiries');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'status_code',
            'path',
            'per_page',
            'total_records',
            'total_pages',
            'current_page',
            'record_range',
            'next',
            'previous',
            'first',
            'last',
            'records',
        ]);
}
```

## Troubleshooting

If you encounter issues running the tests, try the following:

1. Clear the configuration cache:
   ```bash
   php artisan config:clear
   ```

2. Clear the application cache:
   ```bash
   php artisan cache:clear
   ```

3. Regenerate the autoload files:
   ```bash
   composer dump-autoload
   ```

4. Make sure your `.env.testing` file has the correct database configuration

5. Check that all required dependencies are installed:
   ```bash
   composer install
   ```

### Windows-Specific Considerations

If you're running tests on Windows, keep these additional points in mind:

1. Use the `.bat` script instead of the bash script

2. If you encounter path-related issues, make sure to use Windows-style paths with backslashes (`\`) in your configuration files

3. SQLite in-memory database should work fine on Windows, but if you encounter issues, you can use a file-based SQLite database by updating your `.env.testing` file:
   ```
   DB_CONNECTION=sqlite
   DB_DATABASE=D:/path/to/your/project/database/testing.sqlite
   ```
   Make sure to create the empty file first

4. If you're using Git Bash or WSL on Windows, you might need to adjust the paths accordingly

5. For Windows users with XAMPP/WAMP, make sure PHP is in your system PATH or use the full path to PHP when running commands

### Database Considerations

1. **SQLite vs MySQL/MariaDB**: While SQLite is convenient for testing, it has some differences from MySQL/MariaDB that might cause tests to pass locally but fail in production. Consider using the same database engine for testing as you use in production if you encounter such issues.

2. **Database-specific features**: If your application uses MySQL-specific features (like JSON functions, full-text search, or specific transaction behaviors), these might not work the same way in SQLite.

3. **Spatial data**: If you're using spatial data (as indicated by the `Grimzy\LaravelMysqlSpatial` package in your code), SQLite might not support all the spatial functions you need. MySQL would be a better choice in this case.

4. **Performance testing**: If you're testing performance, use the same database engine as production for accurate results.

5. **Database seeding**: When using MySQL for testing, you might need to adjust the seeder to handle foreign key constraints properly.
