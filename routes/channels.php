<?php

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/


Broadcast::channel('LegalChat.{userId}', function ($user, $id) {
   return  (int) $user->id ===  (int) $id;
});
/*Broadcast::channel('MessageSeen.{userId}', function ($user, $id) {
    return  (int) $user->id ===  (int) $id;
});
Broadcast::channel('MessageDelivered.{userId}', function ($user, $id) {
    return  (int) $user->id ===  (int) $id;
});*/

