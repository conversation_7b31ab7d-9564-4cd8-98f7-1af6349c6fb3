<?php

use App\Http\Controllers\Api\V1\Admin\ResourceCenter\Dashboard\DashboardController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\Auth\AuthController;
use App\Http\Controllers\Api\Auth\SocialLoginController;
use App\Http\Controllers\Api\Auth\ForgotPasswordController;
use App\Http\Controllers\Api\Auth\ResetPasswordController;
use App\Http\Controllers\Api\V1\Admin\News\NewsController;
use App\Http\Controllers\Api\V1\Admin\Attorney\AttorneyController;
use App\Http\Controllers\Api\V1\Admin\Client\ClientController;
use App\Http\Controllers\Api\V1\Admin\State\StateController;
use App\Http\Controllers\Api\V1\Admin\PracticeArea\PracticeAreaController;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::group(['middleware' => ['cors', 'json.response']], function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::get('verify-password', [AuthController::class, 'verify']);
    Route::post('password/forgot-password', [ForgotPasswordController::class, 'sendResetLinkResponse']);
    Route::post('password/reset', [ResetPasswordController::class, 'sendResetResponse']);
    Route::post('resend-code',[AuthController::class,'sendAccountVerifyCode']);
});

Route::middleware(['cors', 'json.response', 'auth:api'])->group(function () {
    Route::group(['prefix' => 'news'], function () {
        Route::post('/create', [NewsController::class, 'store']);
        Route::get('/get', [NewsController::class, 'index']);
        Route::get('/get/{id}', [NewsController::class, 'show']);
        Route::put('/update/{id}', [NewsController::class, 'update']);
        Route::delete('/delete/{id}', [NewsController::class, 'destroy']);
    });

Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

Route::resources([
    'attorneys'      => AttorneyController::class,
    'clients'        => ClientController::class,
    'practice-areas' => PracticeAreaController::class,
    'states'         => StateController::class,
]);

    Route::prefix('resource-center')->group(function () {
        // CLE Classes API
        Route::apiResource('cle-classes', App\Http\Controllers\Api\V1\Admin\ResourceCenter\CleClass\CleClassController::class);
        Route::post('cle-classes/{cleClass}/enroll', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\CleClass\CleClassController::class, 'enroll']);
        Route::post('cle-classes/{cleClass}/unenroll', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\CleClass\CleClassController::class, 'unenroll']);
        Route::post('cle-classes/bulk-action', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\CleClass\CleClassController::class, 'bulkAction']);

        // Judges API
        Route::apiResource('judges', App\Http\Controllers\Api\V1\Admin\ResourceCenter\Judge\JudgeController::class);
        Route::get('judges/{judge}/reviews', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Judge\JudgeController::class, 'reviews']);
        Route::post('judges/{judge}/reviews/{review}/approve', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Judge\JudgeController::class, 'approveReview']);
        Route::post('judges/{judge}/reviews/{review}/reject', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Judge\JudgeController::class, 'rejectReview']);
        Route::post('judges/{judge}/reviews/{review}/flag', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Judge\JudgeController::class, 'flagReview']);
        Route::post('judges/bulk-action', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Judge\JudgeController::class, 'bulkAction']);

        // Interpreters API
        Route::apiResource('interpreters', App\Http\Controllers\Api\V1\Admin\ResourceCenter\Interpreter\InterpreterController::class);
        Route::post('interpreters/{interpreter}/update-availability', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Interpreter\InterpreterController::class, 'updateAvailability']);
        Route::post('interpreters/bulk-action', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Interpreter\InterpreterController::class, 'bulkAction']);

        // Translation Services API
        Route::apiResource('translation-services', App\Http\Controllers\Api\V1\Admin\ResourceCenter\TranslationService\TranslationServiceController::class);
        Route::post('translation-services/bulk-action', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\TranslationService\TranslationServiceController::class, 'bulkAction']);

        // Paralegals API
        Route::apiResource('paralegals', App\Http\Controllers\Api\V1\Admin\ResourceCenter\Paralegal\ParalegalController::class);
        Route::post('paralegals/{paralegal}/update-availability', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Paralegal\ParalegalController::class, 'updateAvailability']);
        Route::post('paralegals/bulk-action', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Paralegal\ParalegalController::class, 'bulkAction']);

        // Bondsmen API
        Route::apiResource('bondsmen', App\Http\Controllers\Api\V1\Admin\ResourceCenter\Bondsman\BondsmanController::class);
        Route::post('bondsmen/bulk-action', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\Bondsman\BondsmanController::class, 'bulkAction']);

        // Legal Forum API
        Route::apiResource('forum-categories', App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumCategory\ForumCategoryController::class);
        Route::apiResource('forum-posts', App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumPost\ForumPostController::class);
        Route::post('forum-posts/{post}/pin', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumPost\ForumPostController::class, 'pin']);
        Route::post('forum-posts/{post}/unpin', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumPost\ForumPostController::class, 'unpin']);
        Route::post('forum-posts/{post}/lock', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumPost\ForumPostController::class, 'lock']);
        Route::post('forum-posts/{post}/unlock', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumPost\ForumPostController::class, 'unlock']);
        Route::post('forum-posts/bulk-action', [App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumPost\ForumPostController::class, 'bulkAction']);
    });


});
