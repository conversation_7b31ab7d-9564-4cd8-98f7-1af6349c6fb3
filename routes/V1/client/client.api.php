<?php

use App\Http\Controllers\Api\V1\Client\BrowseAttorney\BrowseAttorneyController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\Client\Address\AddressController;
use App\Http\Controllers\Api\V1\Client\Inquiry\InquiryController;
use App\Http\Controllers\Api\V1\Client\Language\LanguageController;
use App\Http\Controllers\Api\V1\Client\Profile\ProfileController;
use App\Http\Controllers\Api\V1\Client\Rating\RatingController;
use App\Http\Controllers\Api\V1\Client\Support\SupportController;
use App\Http\Controllers\Api\V1\Client\Message\MessageController;
use App\Http\Controllers\Api\V1\Client\Stripe\StripeController;
use App\Http\Controllers\Api\Auth\AuthController;
use App\Http\Controllers\Api\Auth\ForgotPasswordController;
use App\Http\Controllers\Api\Auth\ResetPasswordController;
use App\Http\Controllers\Api\V1\Client\PractiseArea\PracticeAreaController;
use App\Http\Controllers\Api\V1\Client\State\StateController;
use App\Http\Controllers\Api\V1\Client\Proposal\ProposalController;
use App\Http\Controllers\Api\Auth\SocialLoginController;
use App\Http\Controllers\Api\V1\Attorney\UserProfile\UserProfileController;
use App\Http\Controllers\Api\V1\Client\Appointment\AppointmentController;
use App\Http\Controllers\Api\V1\Client\Country\CountryController;
use App\Http\Controllers\Api\V1\Client\News\NewsController;
use App\Http\Controllers\Api\V1\Client\AudioVideo\AudioVideoController;



Route::group(['middleware' => ['cors', 'json.response','userlog','localization']], function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::get('verify-password', [AuthController::class, 'verify']);
    Route::post('password/forgot-password', [ForgotPasswordController::class, 'sendResetLinkResponse']);
    Route::post('password/reset', [ResetPasswordController::class, 'sendResetResponse']);
    Route::post('resend-code',[AuthController::class,'sendAccountVerifyCode']);

    Route::group(['prefix' => 'country'], function () {
        Route::get('get', [CountryController::class, 'index']);
    });
//social login
    //Route::get('/oauth/{provider}', [SocialLoginController::class, 'redirectToProvider']);
    Route::post('/oauth/{provider}/callback', [SocialLoginController::class, 'handleProviderCallback']);

    Route::middleware(['auth:api','localization'])->group(function () {
        Route::post('change-password', [AuthController::class, 'change_password']);
        Route::get('verify', [AuthController::class, 'verify']);
        Route::get('logout', [AuthController::class, 'logout']);
        Route::put('/fcm-token', [AuthController::class, 'updateToken'])->name('fcmToken');
        Route::get('deactivate', [AuthController::class, 'deactivate']);


    });
});

Route::middleware(['cors', 'json.response', 'auth:api','localization'])->group(function () {
    //inquiries route
    Route::group(['prefix' => 'inquiries'], function () {
        Route::get('get', [InquiryController::class, 'index']);
        Route::get('get/{id}', [InquiryController::class, 'show']);
        Route::post('add', [InquiryController::class, 'store']);
        Route::post('{id}/update', [InquiryController::class, 'update']);
        Route::delete('delete/{id}', [InquiryController::class, 'destroy']);
        Route::post('delete-inquiry-attachment', [InquiryController::class, 'deleteInquiryAttachments']);
    });

    Route::group(['prefix' => 'supports'], function () {
        Route::get('/list', [SupportController::class, 'index']);
        Route::get('/category', [SupportController::class, 'category']);
        Route::post('/add', [SupportController::class, 'store']);
        Route::delete('delete/{id}', [SupportController::class, 'destroy']);
    });

    //address routes
    Route::group(['prefix' => 'address'], function () {
        Route::get('get', [AddressController::class, 'index']);
        Route::get('get/{id}', [AddressController::class, 'edit']);
        Route::post('add', [AddressController::class, 'store']);
        Route::put('update', [AddressController::class, 'update']);
        Route::delete('delete/{id}', [AddressController::class, 'destroy']);
    });

    //languages routes
    Route::group(['prefix' => 'language'], function () {
        Route::get('get', [LanguageController::class, 'index']);
        Route::get('get/{id}', [LanguageController::class, 'edit']);
        Route::post('store', [LanguageController::class, 'store']);
        Route::put('update/{id}', [LanguageController::class, 'update']);
        Route::delete('delete/{id}', [LanguageController::class, 'destroy']);
    });

    //practise area
    Route::group(['prefix' => 'practice-area'], function () {
        Route::get('get', [PracticeAreaController::class, 'index']);
    });

    //state route
    Route::group(['prefix' => 'state'], function () {
        Route::get('get', [StateController::class, 'index']);
    });

    //profile routes
    Route::group(['prefix' => 'profile'], function () {
        Route::get('get', [ProfileController::class, 'index']);
        Route::get('get/{id}', [ProfileController::class, 'getById']);
        //Route::get('get/{id}', [ProfileController::class, 'edit']);
        //Route::post('add', [ProfileController::class, 'store']);
        Route::put('update/{id}', [ProfileController::class, 'update']);
        Route::post('avatar', [UserProfileController::class, 'updateAvatar']);
        //Route::delete('delete/{id}', [ProfileController::class, 'destroy']);
    });

    //rating routes
    Route::group(['prefix' => 'rating'], function () {
        Route::get('get', [RatingController::class, 'index']);
        //Route::get('get/{id}', [RatingController::class, 'edit']);
        Route::post('add', [RatingController::class, 'store']);
        //Route::put('update/{id}', [RatingController::class, 'update']);
        //Route::delete('delete/{id}', [RatingController::class, 'destroy']);
    });

    Route::group(['prefix' => 'message'], function () {
        Route::get('fetch', [MessageController::class, 'fetchMessages']);
        Route::get('fetch/{id}', [MessageController::class, 'getMessagesFor']);
        //Route::get('fetch/{id}/{inquiryId}/{proposalId}', [MessageController::class, 'getMessagesFor']);
        Route::post('send', [MessageController::class, 'sendMessage']);
        Route::get('messageCount', [MessageController::class, 'messageCount']);
        Route::get('deliver/{id}', [MessageController::class, 'delivered']);
        Route::delete('/{userId}', [MessageController::class, 'destroy']);
        Route::get('archive/{userId}', [MessageController::class, 'archive']);
        Route::get('seen/{messageId}', [MessageController::class, 'seen']);
        Route::post('notification-check', [MessageController::class, 'notificationCacheCheck']);
    });

    //stripe route
    Route::group(['prefix' => 'stripe'], function(){
        Route::get('setup-intent', [StripeController::class,'getSetupIntent']);
        Route::put('subscription', [StripeController::class,'updateSubscription']);
        Route::post('payments', [StripeController::class,'postPaymentMethods']);
        Route::get('payment-methods', [StripeController::class,'getPaymentMethods']);
        Route::post('remove-payment', [StripeController::class,'removePaymentMethod']);
        Route::post('save-payment', [StripeController::class, 'savePaymentInfo']);
    });

    //proposal route
    Route::group(['prefix' => 'proposal'], function () {
        Route::get('list', [ProposalController::class, 'index']);
        Route::get('accept/{id}', [ProposalController::class, 'accept']);
        Route::get('complete/{id}', [ProposalController::class, 'complete']);
        Route::get('get/{proposalId}', [ProposalController::class, 'get']);
    });

    Route::group(['prefix' => 'appointment'], function () {
        Route::post('create', [AppointmentController::class, 'store']);
        Route::get('get/{participantID}', [AppointmentController::class, 'get']);
        Route::get('cancel/{appointmentID}', [AppointmentController::class, 'cancel']);
        Route::get('approve/{appointmentID}', [AppointmentController::class, 'approve']);
    });

    Route::group(['prefix' => 'news'], function () {
        Route::get('/get', [NewsController::class, 'index']);
        Route::get('/get/{id}', [NewsController::class, 'show']);
    });

    Route::group(['prefix' => 'call'], function () {
        Route::post('create', [AudioVideoController::class, 'create']);
        Route::post('token', [AudioVideoController::class, 'getRtcToken']);
        Route::post('call-end', [AudioVideoController::class, 'callEnd']);
        Route::post('handle-call-action', [AudioVideoController::class, 'handleCallAction']);
    });

    Route::group(['prefix' => 'payment'], function () {
        Route::post('pay', [StripeController::class, 'chargeClient']);
        Route::get('callback', [StripeController::class, 'handleCallback'])->name('payment.callback');

    });

    Route::group(['prefix' => 'browse-attorney'], function () {
        Route::get('list', [BrowseAttorneyController::class, 'index'])->name('browse-attorney.list');
        Route::post('/filter', [BrowseAttorneyController::class, 'filter']);
        Route::post('/search', [BrowseAttorneyController::class, 'search']);
    });




});
