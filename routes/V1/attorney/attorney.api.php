<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\Attorney\WorkExperience\WorkExperienceController;
use App\Http\Controllers\Api\V1\Attorney\Support\SupportController;
use App\Http\Controllers\Api\V1\Attorney\Address\AddressController;
use App\Http\Controllers\Api\V1\Attorney\Language\LanguageController;
use App\Http\Controllers\Api\Auth\AuthController;
use App\Http\Controllers\Api\V1\Attorney\PracticeAreaController;
use App\Http\Controllers\Api\V1\Attorney\StateController;
use App\Http\Controllers\Api\V1\Attorney\CountiesController;
use App\Http\Controllers\Api\V1\Attorney\Registration\RegistrationController;
use App\Http\Controllers\Api\V1\Attorney\UserProfile\UserProfileController;
use App\Http\Controllers\Api\V1\Attorney\ClientInquiry\ClientInquiryController;
use App\Http\Controllers\Api\V1\Attorney\Proposal\ProposalController;
use App\Http\Controllers\Api\Auth\ForgotPasswordController;
use App\Http\Controllers\Api\Auth\ResetPasswordController;
use App\Http\Controllers\Api\V1\Attorney\Rating\RatingController;
use App\Http\Controllers\Api\Auth\SocialLoginController;
use App\Http\Controllers\Api\V1\Client\Message\MessageController;
use App\Http\Controllers\Api\V1\Attorney\Appointment\AppointmentController;
use App\Http\Controllers\Api\V1\Attorney\Country\CountryController;
use App\Http\Controllers\Api\V1\Attorney\News\NewsController;
use App\Http\Controllers\Api\V1\Attorney\AudioVideo\AudioVideoController;
use App\Http\Controllers\Api\V1\Attorney\ResourceCenter\Post\PostController;
use App\Http\Controllers\Api\V1\Attorney\ResourceCenter\PostVisit\PostVisitController;
use App\Http\Controllers\Api\V1\Attorney\ResourceCenter\PostLike\PostLikeController;
use App\Http\Controllers\Api\V1\Attorney\ResourceCenter\PostReply\PostReplyController;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
//'auth:api'

Route::group(['middleware' => ['cors', 'json.response']], function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::get('verify-password', [AuthController::class, 'verify']);
    Route::post('password/forgot-password', [ForgotPasswordController::class, 'sendResetLinkResponse']);
    Route::post('password/reset', [ResetPasswordController::class, 'sendResetResponse']);
    Route::post('resend-code',[AuthController::class,'sendAccountVerifyCode']);

    Route::group(['prefix' => 'country'], function () {
        Route::get('get', [CountryController::class, 'index']);
    });
//social login
    Route::get('/oauth/{provider}', [SocialLoginController::class, 'redirectToProvider']);
    Route::post('/oauth/{provider}/callback', [SocialLoginController::class, 'handleProviderCallback']);

    Route::middleware('auth:api')->group(function () {
        Route::post('change-password', [AuthController::class, 'change_password']);
        Route::get('verify', [AuthController::class, 'verify']);
        Route::get('logout', [AuthController::class, 'logout']);
        Route::put('fcm-token', [AuthController::class, 'updateToken'])->name('fcmToken');
        Route::get('deactivate', [AuthController::class, 'deactivate']);


    });
});

Route::middleware(['cors', 'json.response', 'auth:api'])->group(function () {

    Route::group(['prefix' => 'work-experience'], function () {
        Route::get('get', [WorkExperienceController::class, 'index']);
        Route::post('add', [WorkExperienceController::class, 'store']);
        Route::put('update', [WorkExperienceController::class, 'update']);
        Route::delete('delete/{id}', [WorkExperienceController::class, 'destroy']);
    });

    Route::group(['prefix' => 'supports'], function () {
        Route::get('/list', [SupportController::class, 'index']);
        Route::get('/category', [SupportController::class, 'category']);
        Route::post('/add', [SupportController::class, 'store']);
        Route::delete('delete/{id}', [SupportController::class, 'destroy']);
    });

    Route::group(['prefix' => 'address'], function () {
        Route::get('get', [AddressController::class, 'index']);
        Route::post('add', [AddressController::class, 'store']);
        Route::put('update', [AddressController::class, 'update']);
        Route::delete('delete/{id}', [AddressController::class, 'destroy']);
    });

    Route::group(['prefix' => 'language'], function () {
        Route::get('get', [LanguageController::class, 'index']);
        Route::post('store', [LanguageController::class, 'store']);
    });

    Route::group(['prefix' => 'practice-area'], function () {
        Route::get('get', [PracticeAreaController::class, 'index']);
        Route::put('update', [PracticeAreaController::class, 'update']);
    });
    Route::group(['prefix' => 'state'], function () {
        Route::get('get', [StateController::class, 'index']);
        Route::get('county', [StateController::class, 'getCounties']);
        Route::put('update', [StateController::class, 'update']);
    });
    Route::group(['prefix' => 'county'], function () {
        Route::get('get', [CountiesController::class, 'index']);
    });
    Route::group(['prefix' => 'profile'], function () {
        Route::post('submit', [RegistrationController::class, 'index']);
        Route::get('get', [UserProfileController::class, 'index']);
        Route::get('get/{id}', [UserProfileController::class, 'getById']);
        Route::put('update-bio', [UserProfileController::class, 'updateBio']);
        Route::post('avatar', [UserProfileController::class, 'updateAvatar']);
        Route::post('update-consultation-amount', [UserProfileController::class, 'updateConsultationAmount']);
    });

    Route::group(['prefix' => 'inquiry'], function () {
        Route::get('list', [ClientInquiryController::class, 'index']);
        Route::get('get/{inquiryID}', [ClientInquiryController::class, 'get']);
        Route::post('dispensation', [ClientInquiryController::class, 'dispensation']);
    });

    //rating routes
    Route::group(['prefix' => 'rating'], function () {
        Route::get('get', [RatingController::class, 'index']);
        //Route::get('get/{id}', [RatingController::class, 'edit']);
        Route::post('add', [RatingController::class, 'store']);
        //Route::put('update/{id}', [RatingController::class, 'update']);
        //Route::delete('delete/{id}', [RatingController::class, 'destroy']);
    });

     Route::group(['prefix' => 'message'], function () {
        Route::get('fetch', [MessageController::class, 'fetchMessages']);
        Route::get('fetch/{id}', [MessageController::class, 'getMessagesFor']);
        // Route::get('fetch/{id}/{inquiryId}/{proposalId}', [MessageController::class, 'getMessagesFor']);
        Route::post('send', [MessageController::class, 'sendMessage']);
         Route::get('messageCount', [MessageController::class, 'messageCount']);
         Route::get('deliver/{id}', [MessageController::class, 'delivered']);
         Route::delete('/{userId}', [MessageController::class, 'destroy']);
         Route::get('archive/{userId}', [MessageController::class, 'archive']);
         Route::get('seen/{messageId}', [MessageController::class, 'seen']);
         Route::post('notification-check', [MessageController::class, 'notificationCacheCheck']);
    });

    Route::group(['prefix' => 'proposal'], function () {
        Route::get('list', [ProposalController::class, 'index']);
        Route::post('submit', [ProposalController::class, 'store']);
        Route::delete('cancel/{proposalID}', [ProposalController::class, 'cancelProposal']);
        Route::get('complete/{id}', [ProposalController::class, 'complete']);
        Route::get('get/{inquiryID}', [ProposalController::class, 'get']);
    });

    Route::group(['prefix' => 'appointment'], function () {
        Route::post('create', [AppointmentController::class, 'store']);
        Route::get('/', [AppointmentController::class, 'index']);
        Route::get('get/{participantID}', [AppointmentController::class, 'get']);
        Route::delete('delete/{appointmentID}', [AppointmentController::class, 'delete']);
        Route::put('approve/{appointmentID}', [AppointmentController::class, 'approve']);
    });

    Route::group(['prefix' => 'news'], function () {
        Route::get('/get', [NewsController::class, 'index']);
        Route::get('/get/{id}', [NewsController::class, 'show']);
    });

    Route::group(['prefix' => 'call'], function () {
        Route::post('create', [AudioVideoController::class, 'create']);
        Route::post('token', [AudioVideoController::class, 'getRtcToken']);
        Route::post('call-end', [AudioVideoController::class, 'callEnd']);
        Route::post('handle-call-action', [AudioVideoController::class, 'handleCallAction']);
    });

    Route::group(['prefix' => 'post'], function () {
        Route::get('/', [PostController::class, 'index']);
        Route::post('/add', [PostController::class, 'store']);
        Route::get('/discover', [PostController::class, 'discover']);
        Route::get('/get/{id}', [PostController::class, 'show']);
        Route::delete('/{postID}', [PostController::class, 'destroy']);
        Route::post('/search', [PostController::class, 'search']);

    });

    Route::group(['prefix' => 'post-visit'], function () {
        Route::post('/', [PostVisitController::class, 'index']);

    });

    Route::group(['prefix' => 'post-like'], function () {
        Route::post('/', [PostLikeController::class, 'index']);

    });

    Route::group(['prefix' => 'post-reply'], function () {
        Route::post('/{postID}', [PostReplyController::class, 'index']);

    });

});
