#!/bin/bash

# Set environment to testing
export APP_ENV=testing

# Clear cache
echo "Clearing cache..."
php artisan config:clear
php artisan cache:clear

# Prepare the test database
echo "Preparing test database..."

# Create the testing database if it doesn't exist
echo "Creating testing database if it doesn't exist..."
mysql -u root -e "CREATE DATABASE IF NOT EXISTS legalfiber_testing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run migrations and seed the database
php artisan migrate:fresh --env=testing
php artisan db:seed --class="Database\\Seeders\\TestDatabaseSeeder" --env=testing

# Run all tests
echo -e "\n\n=== Running All Tests ===\n"
php artisan test --env=testing

# Run specific test groups
echo -e "\n\n=== Running Auth Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Auth

echo -e "\n\n=== Running Attorney Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney

echo -e "\n\n=== Running Client Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Client

# Run specific controller tests
echo -e "\n\n=== Running ClientInquiry Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/ClientInquiry/ClientInquiryControllerTest.php

echo -e "\n\n=== Running Auth Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Auth/AuthControllerTest.php

echo -e "\n\n=== Running News Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/News/NewsControllerTest.php

echo -e "\n\n=== Running Proposal Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/Proposal/ProposalControllerTest.php

echo -e "\n\n=== Running WorkExperience Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/WorkExperience/WorkExperienceControllerTest.php

echo -e "\n\n=== Running Address Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/Address/AddressControllerTest.php

echo -e "\n\n=== Running Language Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/Language/LanguageControllerTest.php

echo -e "\n\n=== Running PracticeArea Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/PracticeAreaControllerTest.php

echo -e "\n\n=== Running Appointment Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/Appointment/AppointmentControllerTest.php

echo -e "\n\n=== Running Rating Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/Rating/RatingControllerTest.php

echo -e "\n\n=== Running UserProfile Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Attorney/UserProfile/UserProfileControllerTest.php

echo -e "\n\n=== Running Message Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Client/Message/MessageControllerTest.php

echo -e "\n\n=== Running ForgotPassword Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Auth/ForgotPasswordControllerTest.php

echo -e "\n\n=== Running ResetPassword Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Auth/ResetPasswordControllerTest.php

echo -e "\n\n=== Running SocialLogin Controller Tests ===\n"
php artisan test --env=testing tests/Feature/Auth/SocialLoginControllerTest.php

echo -e "\n\n=== All Tests Completed ===\n"
