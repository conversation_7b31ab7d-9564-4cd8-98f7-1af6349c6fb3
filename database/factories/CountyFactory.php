<?php

namespace Database\Factories;

use App\Models\County;
use App\Models\State;
use Grimzy\LaravelMysqlSpatial\Types\Point;
use Grimzy\LaravelMysqlSpatial\Types\Polygon;
use Illuminate\Database\Eloquent\Factories\Factory;

class CountyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = County::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create a state if none exists
        $state = State::first() ?? State::factory()->create();

        // Create a simple polygon for testing
        $lat = $this->faker->latitude;
        $lng = $this->faker->longitude;
        $polygon = new Polygon([
            new Point($lat, $lng),
            new Point($lat + 0.1, $lng),
            new Point($lat + 0.1, $lng + 0.1),
            new Point($lat, $lng + 0.1),
            new Point($lat, $lng),
        ]);

        return [
            'name' => $this->faker->city . ' County',
            'state_id' => $state->id,
            'geometry' => $polygon,
        ];
    }
}
