<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'phone_code' => '+1',
            'email_verified_at' => now(),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'remember_token' => Str::random(10),
            'user_type' => 'CLIENT',
            'status' => 1,
            'avatar' => "https://ui-avatars.com/api/?background=ca313b&color=fff&rounded=true&bold=true&name=" . $this->faker->name(),
            'profile_points' => 0,
            'fcm_token' => null,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function unverified()
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => null,
            ];
        });
    }

    /**
     * Configure the model factory for an attorney user.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function attorney()
    {
        return $this->state(function (array $attributes) {
            return [
                'user_type' => 'ATTORNEY',
            ];
        });
    }

    /**
     * Configure the model factory for a client user.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function client()
    {
        return $this->state(function (array $attributes) {
            return [
                'user_type' => 'CLIENT',
            ];
        });
    }

    /**
     * Configure the model factory for an admin user.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function admin()
    {
        return $this->state(function (array $attributes) {
            return [
                'user_type' => 'ADMIN',
            ];
        });
    }
}
