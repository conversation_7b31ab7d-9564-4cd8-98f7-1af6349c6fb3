<?php

namespace Database\Factories;

use App\Models\Inquiry;
use App\Models\Proposal;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProposalFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Proposal::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $attorney = User::where('user_type', 'ATTORNEY')->first() ?? User::factory()->attorney()->create();
        $inquiry = Inquiry::first() ?? Inquiry::factory()->create();

        return [
            'user_id' => $attorney->id,
            'inquiry_id' => $inquiry->id,
            'message' => $this->faker->paragraph,
            'amount' => $this->faker->randomFloat(2, 100, 5000),
            'currency' => 'USD',
            'status' => $this->faker->randomElement([
                Proposal::PENDING,
                Proposal::ACTIVE,
                Proposal::CANCELLED,
                Proposal::COMPLETED
            ]),
        ];
    }

    /**
     * Configure the model factory for a pending proposal.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => Proposal::PENDING,
            ];
        });
    }

    /**
     * Configure the model factory for an active proposal.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => Proposal::ACTIVE,
            ];
        });
    }

    /**
     * Configure the model factory for a cancelled proposal.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => Proposal::CANCELLED,
            ];
        });
    }

    /**
     * Configure the model factory for a completed proposal.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function completed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => Proposal::COMPLETED,
            ];
        });
    }
}
