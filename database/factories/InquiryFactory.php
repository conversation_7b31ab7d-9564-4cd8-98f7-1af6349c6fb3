<?php

namespace Database\Factories;

use App\Models\County;
use App\Models\Inquiry;
use App\Models\State;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class InquiryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Inquiry::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $user = User::where('user_type', 'CLIENT')->first() ?? User::factory()->client()->create();
        $state = State::first() ?? State::factory()->create();
        $county = County::where('state_id', $state->id)->first() ?? County::factory()->create(['state_id' => $state->id]);

        return [
            'title' => $this->faker->sentence,
            'message' => $this->faker->paragraph,
            'user_id' => $user->id,
            'state_id' => $state->id,
            'county_id' => $county->id,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'is_published' => true,
        ];
    }
}
