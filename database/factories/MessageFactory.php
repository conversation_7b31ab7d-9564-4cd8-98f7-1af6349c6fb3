<?php

namespace Database\Factories;

use App\Models\Message;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class MessageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Message::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $sender = User::first() ?? User::factory()->create();
        
        // Create a different user for the receiver
        $receiver = User::where('id', '!=', $sender->id)->first();
        if (!$receiver) {
            $receiver = User::factory()->create();
        }

        return [
            'from_user_id' => $sender->id,
            'to_user_id' => $receiver->id,
            'message' => $this->faker->sentence,
            'type' => 'text',
            'read_status' => $this->faker->boolean(70),
            'delivered' => true,
        ];
    }

    /**
     * Configure the model factory for an unread message.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function unread()
    {
        return $this->state(function (array $attributes) {
            return [
                'read_status' => false,
            ];
        });
    }

    /**
     * Configure the model factory for a read message.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function read()
    {
        return $this->state(function (array $attributes) {
            return [
                'read_status' => true,
            ];
        });
    }
}
