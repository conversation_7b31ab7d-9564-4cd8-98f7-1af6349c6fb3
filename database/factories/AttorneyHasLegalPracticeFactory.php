<?php

namespace Database\Factories;

use App\Models\AttorneyHasLegalPractice;
use App\Models\PracticeArea;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttorneyHasLegalPracticeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AttorneyHasLegalPractice::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $attorney = User::where('user_type', 'ATTORNEY')->first() ?? User::factory()->attorney()->create();
        $practiceArea = PracticeArea::first() ?? PracticeArea::factory()->create();

        return [
            'user_id' => $attorney->id,
            'practice_area_id' => $practiceArea->id,
        ];
    }
}
