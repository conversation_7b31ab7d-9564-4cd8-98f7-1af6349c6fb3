<?php

namespace Database\Factories;

use App\Models\Appointment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class AppointmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Appointment::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $attorney = User::where('user_type', 'ATTORNEY')->first() ?? User::factory()->attorney()->create();
        $client = User::where('user_type', 'CLIENT')->first() ?? User::factory()->client()->create();

        return [
            'attorney_id' => $attorney->id,
            'client_id' => $client->id,
            'title' => $this->faker->sentence,
            'description' => $this->faker->paragraph,
            'time' => Carbon::now()->addDays($this->faker->numberBetween(1, 30))->format('Y-m-d H:i'),
            'duration' => $this->faker->randomElement([30, 60, 90, 120]),
            'meeting_type' => $this->faker->randomElement(['video', 'phone', 'in-person']),
            'status' => $this->faker->randomElement(['pending', 'confirmed', 'cancelled', 'completed']),
        ];
    }

    /**
     * Configure the model factory for a pending appointment.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
            ];
        });
    }

    /**
     * Configure the model factory for a confirmed appointment.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function confirmed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'confirmed',
            ];
        });
    }

    /**
     * Configure the model factory for a cancelled appointment.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'cancelled',
            ];
        });
    }

    /**
     * Configure the model factory for a completed appointment.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function completed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'completed',
            ];
        });
    }
}
