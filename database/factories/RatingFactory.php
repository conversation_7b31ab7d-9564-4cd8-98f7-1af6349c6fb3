<?php

namespace Database\Factories;

use App\Models\Rating;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class RatingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Rating::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $attorney = User::where('user_type', 'ATTORNEY')->first() ?? User::factory()->attorney()->create();
        $client = User::where('user_type', 'CLIENT')->first() ?? User::factory()->client()->create();

        return [
            'attorney_id' => $attorney->id,
            'client_id' => $client->id,
            'rating' => $this->faker->numberBetween(1, 5),
            'comment' => $this->faker->paragraph,
        ];
    }
}
