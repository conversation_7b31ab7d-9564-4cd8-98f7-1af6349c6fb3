<?php

namespace Database\Factories;

use App\Models\Attorney\AttroneyHasStates;
use App\Models\State;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttorneyHasStatesFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AttroneyHasStates::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $attorney = User::where('user_type', 'ATTORNEY')->first() ?? User::factory()->attorney()->create();
        $state = State::first() ?? State::factory()->create();

        return [
            'user_id' => $attorney->id,
            'state_id' => $state->id,
            'bar_date' => $this->faker->date(),
        ];
    }
}
