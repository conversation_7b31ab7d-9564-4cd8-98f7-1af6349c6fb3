<?php

namespace Database\Factories;

use App\Models\Address;
use App\Models\Country;
use App\Models\State;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class AddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Address::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $user = User::first() ?? User::factory()->create();
        $state = State::first() ?? State::factory()->create();
        $country = Country::first() ?? Country::factory()->create();

        return [
            'address_line_1' => $this->faker->streetAddress,
            'address_line_2' => $this->faker->secondaryAddress,
            'user_id' => $user->id,
            'city' => $this->faker->city,
            'state_id' => $state->id,
            'country_id' => $country->id,
            'zip_code' => $this->faker->postcode,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
        ];
    }
}
