<?php

namespace Database\Factories;

use App\Models\SupportCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

class SupportCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SupportCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $categories = [
            'Technical Issue',
            'Billing Question',
            'Account Access',
            'Feature Request',
            'Bug Report',
            'General Inquiry',
            'Feedback',
            'Other',
        ];

        return [
            'name' => $this->faker->unique()->randomElement($categories),
        ];
    }
}
