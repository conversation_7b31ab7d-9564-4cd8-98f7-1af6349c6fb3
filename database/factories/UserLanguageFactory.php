<?php

namespace Database\Factories;

use App\Models\Language;
use App\Models\User;
use App\Models\UserLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserLanguageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserLanguage::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $user = User::first() ?? User::factory()->create();
        $language = Language::first() ?? Language::factory()->create();

        return [
            'user_id' => $user->id,
            'language_id' => $language->id,
            'proficiency' => $this->faker->randomElement(['beginner', 'intermediate', 'fluent', 'native']),
        ];
    }
}
