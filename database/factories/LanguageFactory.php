<?php

namespace Database\Factories;

use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\Factory;

class LanguageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Language::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $languages = [
            'English',
            'Spanish',
            'French',
            'German',
            'Italian',
            'Portuguese',
            'Russian',
            'Japanese',
            'Chinese',
            'Arabic',
            'Hindi',
            'Bengali',
            'Korean',
            'Vietnamese',
            'Thai',
        ];

        return [
            'name' => $this->faker->unique()->randomElement($languages),
        ];
    }
}
