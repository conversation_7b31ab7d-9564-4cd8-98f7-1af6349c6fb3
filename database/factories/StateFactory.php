<?php

namespace Database\Factories;

use App\Models\Country;
use App\Models\State;
use Illuminate\Database\Eloquent\Factories\Factory;

class StateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = State::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create a country if none exists
        $country = Country::first() ?? Country::factory()->create();

        return [
            'name' => $this->faker->unique()->state,
            'abbr' => $this->faker->unique()->stateAbbr,
            'country_id' => $country->id,
        ];
    }
}
