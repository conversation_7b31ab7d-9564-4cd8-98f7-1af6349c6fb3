<?php

namespace Database\Factories;

use App\Models\PracticeArea;
use Illuminate\Database\Eloquent\Factories\Factory;

class PracticeAreaFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PracticeArea::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $practiceAreas = [
            'Family Law',
            'Criminal Law',
            'Personal Injury',
            'Estate Planning',
            'Business Law',
            'Intellectual Property',
            'Immigration Law',
            'Tax Law',
            'Employment Law',
            'Real Estate Law',
            'Bankruptcy Law',
            'Civil Litigation',
            'Environmental Law',
            'Healthcare Law',
            'Constitutional Law',
        ];

        return [
            'name' => $this->faker->unique()->randomElement($practiceAreas),
        ];
    }
}
