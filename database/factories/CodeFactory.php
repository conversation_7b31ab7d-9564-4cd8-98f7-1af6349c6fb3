<?php

namespace Database\Factories;

use App\Models\Code;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CodeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Code::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create a user if none exists
        $user = User::first() ?? User::factory()->create();

        return [
            'code' => $this->faker->numerify('######'), // 6-digit code
            'user_id' => $user->id,
            'type' => $this->faker->randomElement(['1', '2']), // 1 for reset password, 2 for verify account
        ];
    }

    /**
     * Configure the model factory for a password reset code.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function passwordReset()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => '1',
            ];
        });
    }

    /**
     * Configure the model factory for an account verification code.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function accountVerification()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => '2',
            ];
        });
    }
}
