<?php

namespace Database\Factories;

use App\Models\Support;
use App\Models\SupportCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class SupportFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Support::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create dependencies if they don't exist
        $user = User::first() ?? User::factory()->create();
        $category = SupportCategory::first() ?? SupportCategory::factory()->create();

        return [
            'title' => $this->faker->sentence,
            'description' => $this->faker->paragraph,
            'is_resolved' => $this->faker->boolean(30),
            'support_category_id' => $category->id,
            'user_id' => $user->id,
        ];
    }

    /**
     * Configure the model factory for a resolved support ticket.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function resolved()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_resolved' => true,
            ];
        });
    }

    /**
     * Configure the model factory for an unresolved support ticket.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function unresolved()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_resolved' => false,
            ];
        });
    }
}
