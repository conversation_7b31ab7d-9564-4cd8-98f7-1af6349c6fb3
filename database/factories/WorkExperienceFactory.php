<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\WorkExperience;
use Illuminate\Database\Eloquent\Factories\Factory;

class WorkExperienceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WorkExperience::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create an attorney if none exists
        $attorney = User::where('user_type', 'ATTORNEY')->first() ?? User::factory()->attorney()->create();
        
        $startDate = $this->faker->dateTimeBetween('-10 years', '-1 year');
        $endDate = $this->faker->boolean(70) 
            ? $this->faker->dateTimeBetween($startDate, 'now') 
            : null;
        
        return [
            'user_id' => $attorney->id,
            'company_name' => $this->faker->company,
            'job_title' => $this->faker->jobTitle,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'is_current' => $endDate === null,
            'description' => $this->faker->paragraph,
        ];
    }

    /**
     * Configure the model factory for a current job.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function current()
    {
        return $this->state(function (array $attributes) {
            return [
                'end_date' => null,
                'is_current' => true,
            ];
        });
    }
}
