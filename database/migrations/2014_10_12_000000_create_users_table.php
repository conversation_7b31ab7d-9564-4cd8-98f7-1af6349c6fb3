<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration
{
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {

            $table->bigIncrements('id')->unsigned();
            $table->string('name');
            $table->string('email');
            $table->string('phone',15)->nullable()->default(NULL);
            $table->timestamp('email_verified_at')->nullable()->default(NULL);
            $table->string('password')->nullable()->default(NULL);
            $table->integer('status')->default(0);
            $table->string('remember_token',100)->nullable()->default(NULL);
            $table->timestamp('created_at')->nullable()->default(NULL);
            $table->timestamp('updated_at')->nullable()->default(NULL);
            $table->enum('user_type',['ADMIN','CLIENT','ATTORNEY']);
            $table->string('photo')->nullable()->default(NULL);

        });
    }

    public function down()
    {
        Schema::dropIfExists('users');
    }
}
