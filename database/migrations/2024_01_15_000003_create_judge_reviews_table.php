<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('judge_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('judge_id')->constrained()->onDelete('cascade');
            $table->string('reviewer_hash'); // Anonymous hash for reviewer identification
            $table->integer('overall_rating'); // 1-5 stars
            $table->json('category_ratings'); // fairness, knowledge, temperament, efficiency, communication
            $table->text('comment')->nullable();
            $table->string('case_type')->nullable(); // Civil, Criminal, Family, etc.
            $table->enum('status', ['approved', 'pending', 'flagged', 'rejected'])->default('pending');
            $table->string('flag_reason')->nullable();
            $table->timestamp('reviewed_at')->nullable(); // When admin reviewed it
            $table->foreignId('reviewed_by')->nullable()->constrained('users'); // Admin who reviewed
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamps();
            
            $table->index(['judge_id', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('reviewer_hash');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('judge_reviews');
    }
};
