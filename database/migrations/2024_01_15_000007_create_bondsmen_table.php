<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bondsmen', function (Blueprint $table) {
            $table->id();
            $table->string('company_name');
            $table->string('contact_person');
            $table->string('email');
            $table->string('phone');
            $table->string('emergency_phone')->nullable();
            $table->string('website')->nullable();
            $table->text('address')->nullable();
            $table->string('city');
            $table->string('state');
            $table->string('zip_code')->nullable();
            $table->json('service_areas'); // Counties/regions they serve
            $table->json('bond_types'); // Felony, Misdemeanor, Federal, etc.
            $table->decimal('minimum_bond', 10, 2);
            $table->decimal('maximum_bond', 12, 2);
            $table->decimal('fee_percentage', 5, 2); // Fee percentage (e.g., 10%)
            $table->enum('availability', ['24/7', 'business-hours', 'on-call'])->default('business-hours');
            $table->decimal('rating', 3, 2)->default(0);
            $table->integer('total_bonds')->default(0);
            $table->string('license_number');
            $table->decimal('insurance_amount', 12, 2);
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->date('joined_date');
            $table->timestamp('last_active')->nullable();
            $table->text('description')->nullable();
            $table->json('accepted_payment_methods')->nullable(); // Cash, Credit, Check, etc.
            $table->boolean('collateral_accepted')->default(true);
            $table->json('collateral_types')->nullable(); // Real Estate, Vehicles, etc.
            $table->string('business_hours')->nullable();
            $table->boolean('mobile_service')->default(false);
            $table->decimal('mobile_service_fee', 8, 2)->nullable();
            $table->date('license_expiry')->nullable();
            $table->string('bonding_company')->nullable(); // Insurance company backing them
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['city', 'state']);
            $table->index(['availability', 'status']);
            $table->index('license_number');
            $table->index('rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bondsmen');
    }
};
