<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paralegals', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone');
            $table->text('address')->nullable();
            $table->string('city');
            $table->string('state');
            $table->string('zip_code')->nullable();
            $table->json('specializations'); // Corporate Law, Criminal Law, etc.
            $table->integer('experience_years');
            $table->json('education'); // Degrees, certificates, schools
            $table->json('certifications'); // NALA Certified, NFPA Certified, etc.
            $table->enum('availability', ['available', 'busy', 'unavailable'])->default('available');
            $table->enum('employment_type', ['full-time', 'part-time', 'contract', 'freelance']);
            $table->decimal('hourly_rate', 8, 2);
            $table->decimal('rating', 3, 2)->default(0);
            $table->integer('total_jobs')->default(0);
            $table->json('skills'); // Legal Research, Document Drafting, etc.
            $table->enum('status', ['active', 'inactive', 'pending'])->default('pending');
            $table->date('joined_date');
            $table->timestamp('last_active')->nullable();
            $table->text('bio')->nullable();
            $table->string('resume_url')->nullable();
            $table->string('linkedin')->nullable();
            $table->json('software_proficiency')->nullable(); // Legal software they know
            $table->boolean('remote_work_available')->default(false);
            $table->boolean('travel_available')->default(false);
            $table->json('languages_spoken')->nullable();
            $table->string('bar_admission')->nullable(); // If applicable
            $table->boolean('notary_public')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['city', 'state']);
            $table->index(['availability', 'employment_type']);
            $table->index(['status', 'is_active']);
            $table->index('rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paralegals');
    }
};
