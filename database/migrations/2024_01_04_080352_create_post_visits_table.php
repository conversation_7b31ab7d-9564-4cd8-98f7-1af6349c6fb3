<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePostVisitsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if(!Schema::hasTable('post_visits')) {
            Schema::create('post_visits', function (Blueprint $table) {
                $table->id();
                $table->foreignId('post_id')->constrained('posts');
                $table->foreignId('user_id')->constrained('users');
                $table->string('visitor_ip')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('post_visits');
    }
}
