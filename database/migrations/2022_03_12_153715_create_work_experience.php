<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWorkExperiencesTable extends Migration
{
    public function up()
    {
        Schema::create('work_experiences', function (Blueprint $table) {

            $table->bigIncrements('id')->unsigned();
            $table->string('company');
            $table->string('position')->nullable()->default('NULL');
            $table->date('joined_date');
            $table->date('left_date')->nullable()->default('NULL');
            $table->tinyInteger('is_working',1)->nullable()->default('NULL');
            $table->bigInteger('user_id',20)->unsigned();
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
            $table->primary('id');
            $table->foreign('user_id')->references('id')->on('users');
        });
    }

    public function down()
    {
        Schema::dropIfExists('work_experiences');
    }
}
