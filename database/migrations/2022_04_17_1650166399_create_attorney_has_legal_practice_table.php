<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAttorneyHasLegalPracticeTable extends Migration
{
    public function up()
    {
        Schema::create('attorney_has_legal_practice', function (Blueprint $table) {
		$table->id();
		$table->bigInteger('user_id')->unsigned();
		$table->bigInteger('practice_area_id')->unsigned();
		$table->timestamps();
		$table->foreign('user_id')->references('id')->on('users');		$table->foreign('practice_area_id')->references('id')->on('practice_areas');
        });
    }

    public function down()
    {
        Schema::dropIfExists('attorney_has_legal_practice');
    }
}
