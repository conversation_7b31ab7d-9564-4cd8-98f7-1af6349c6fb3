<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateForumMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if(!Schema::hasTable('posts')) {
        Schema::create('posts', function (Blueprint $table) {
            $table->id();
            $table->text('title');
            $table->foreignId('user_id')->constrained('users');
            $table->foreignId('anonymous_user_id')->nullable();
            $table->text('content');
            $table->index(['user_id', 'anonymous_user_id']); // Index for better performance
            $table->timestamps();
        });
        }

        if(!Schema::hasTable('post_tags')) {
            Schema::create('post_tags', function (Blueprint $table) {
                $table->id();
                $table->string('name', 255);
                // Add other tag-related fields as needed
                $table->timestamps();
            });
        }


        if(!Schema::hasTable('post_likes')) {
            Schema::create('post_likes', function (Blueprint $table) {
                $table->id();
                $table->foreignId('post_id')->constrained('posts');
                $table->foreignId('user_id')->constrained('users');
                $table->foreignId('anonymous_user_id')->nullable();
                $table->dateTime('like_date');
                // Add other like-related fields as needed
                $table->index(['user_id', 'anonymous_user_id']); // Index for better performance
                $table->timestamps();
            });
        }

        if(!Schema::hasTable('post_replies')) {
            Schema::create('post_replies', function (Blueprint $table) {
                $table->id();
                $table->foreignId('post_id')->constrained('posts');
                $table->foreignId('user_id')->constrained('users');
                $table->foreignId('anonymous_user_id')->nullable();
                $table->text('content');
                $table->dateTime('reply_date');
                $table->foreignId('parent_reply_id')->nullable()->constrained('post_replies', 'id');

                // Add other reply-related fields as needed
                $table->index(['user_id', 'anonymous_user_id']); // Index for better performance
                $table->timestamps();
            });
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('posts');
        Schema::dropIfExists('post_tags');
        Schema::dropIfExists('topic_tags');
        Schema::dropIfExists('post_likes');
        Schema::dropIfExists('post_replies');
    }
}
