<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cle_classes', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->string('instructor');
            $table->date('date');
            $table->time('time');
            $table->integer('duration'); // in hours
            $table->string('location');
            $table->enum('type', ['online', 'in-person', 'hybrid']);
            $table->integer('credits');
            $table->integer('capacity');
            $table->integer('enrolled')->default(0);
            $table->enum('status', ['scheduled', 'ongoing', 'completed', 'cancelled'])->default('scheduled');
            $table->decimal('price', 8, 2);
            $table->string('category');
            $table->json('requirements')->nullable(); // Prerequisites, materials, etc.
            $table->string('meeting_url')->nullable(); // For online classes
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['date', 'status']);
            $table->index(['category', 'is_active']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cle_classes');
    }
};
