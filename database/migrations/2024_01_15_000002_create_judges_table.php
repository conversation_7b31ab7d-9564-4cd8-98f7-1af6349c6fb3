<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('judges', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('court');
            $table->string('jurisdiction');
            $table->string('position')->nullable(); // Chief Judge, Associate Judge, etc.
            $table->text('bio')->nullable();
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('total_ratings')->default(0);
            $table->json('category_ratings')->nullable(); // fairness, knowledge, temperament, etc.
            $table->enum('status', ['active', 'under_review', 'suspended'])->default('active');
            $table->string('photo_url')->nullable();
            $table->json('specializations')->nullable(); // Areas of law they handle
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['court', 'jurisdiction']);
            $table->index(['status', 'is_active']);
            $table->index('average_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('judges');
    }
};
