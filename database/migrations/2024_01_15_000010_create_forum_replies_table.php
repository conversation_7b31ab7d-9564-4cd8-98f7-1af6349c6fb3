<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forum_replies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('post_id')->constrained('forum_posts')->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('parent_id')->nullable()->constrained('forum_replies')->onDelete('cascade'); // For nested replies
            $table->longText('content');
            $table->enum('status', ['published', 'pending', 'flagged', 'deleted'])->default('pending');
            $table->integer('likes')->default(0);
            $table->string('flag_reason')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users');
            $table->json('attachments')->nullable();
            $table->string('ip_address')->nullable();
            $table->boolean('anonymous')->default(false);
            $table->timestamps();
            
            $table->index(['post_id', 'status', 'created_at']);
            $table->index(['user_id', 'status']);
            $table->index(['parent_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forum_replies');
    }
};
