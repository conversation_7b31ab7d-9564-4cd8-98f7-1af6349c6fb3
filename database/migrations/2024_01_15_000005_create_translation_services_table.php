<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('translation_services', function (Blueprint $table) {
            $table->id();
            $table->string('company_name');
            $table->string('contact_person');
            $table->string('email');
            $table->string('phone');
            $table->string('emergency_phone')->nullable();
            $table->string('website')->nullable();
            $table->text('address')->nullable();
            $table->string('city');
            $table->string('state');
            $table->string('zip_code')->nullable();
            $table->json('languages'); // Language pairs they handle
            $table->json('document_types'); // Contracts, Court Documents, etc.
            $table->json('specializations'); // Legal, Medical, Technical, etc.
            $table->json('certifications'); // ATA Certified, ISO 17100, etc.
            $table->string('turnaround_time'); // 24-48 hours, etc.
            $table->decimal('price_per_word', 6, 4); // Price per word
            $table->decimal('price_per_page', 8, 2); // Price per page
            $table->decimal('rush_fee_multiplier', 3, 2)->default(1.5); // Rush fee multiplier
            $table->decimal('rating', 3, 2)->default(0);
            $table->integer('total_jobs')->default(0);
            $table->enum('status', ['active', 'inactive', 'pending'])->default('pending');
            $table->date('joined_date');
            $table->timestamp('last_active')->nullable();
            $table->text('description')->nullable();
            $table->json('service_areas')->nullable(); // Geographic coverage
            $table->boolean('notarization_available')->default(false);
            $table->boolean('rush_service_available')->default(true);
            $table->integer('minimum_order')->nullable(); // Minimum order amount
            $table->decimal('insurance_amount', 12, 2)->nullable();
            $table->string('license_number')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['city', 'state']);
            $table->index(['status', 'is_active']);
            $table->index('rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('translation_services');
    }
};
