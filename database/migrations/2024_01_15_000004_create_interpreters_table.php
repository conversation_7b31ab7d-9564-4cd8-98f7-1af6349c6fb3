<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interpreters', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone');
            $table->string('emergency_phone')->nullable();
            $table->text('address')->nullable();
            $table->string('city');
            $table->string('state');
            $table->string('zip_code')->nullable();
            $table->json('languages'); // Array of languages they speak
            $table->json('specializations'); // Legal, Medical, Business, etc.
            $table->json('certifications'); // Court Certified, ATA Certified, etc.
            $table->enum('availability', ['available', 'busy', 'unavailable'])->default('available');
            $table->decimal('rating', 3, 2)->default(0);
            $table->integer('total_jobs')->default(0);
            $table->decimal('hourly_rate', 8, 2);
            $table->integer('experience_years');
            $table->enum('status', ['active', 'inactive', 'pending'])->default('pending');
            $table->date('joined_date');
            $table->timestamp('last_active')->nullable();
            $table->text('bio')->nullable();
            $table->string('website')->nullable();
            $table->string('linkedin')->nullable();
            $table->json('service_areas')->nullable(); // Counties/regions they serve
            $table->boolean('travel_available')->default(false);
            $table->decimal('travel_rate', 8, 2)->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['city', 'state']);
            $table->index(['availability', 'status']);
            $table->index('rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interpreters');
    }
};
