<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSupportDocument extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('support_has_documents', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('support_id')->unsigned();
            $table->string('file');
            $table->string('file_name');
            $table->string('attachment_id');
            $table->timestamps();
            $table->foreign('support_id')->references('id')->on('supports')->onDelete('cascade');
            $table->foreign('attachment_id')->references('id')->on('attachments')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('support_has_documents');
    }
}
