<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAttorneyHasCountiesTable extends Migration
{
    public function up()
    {
        Schema::create('attorney_has_counties', function (Blueprint $table) {

            $table->bigIncrements('id');
            $table->bigInteger('attorney_state_id')->unsigned();
            $table->bigInteger('county_id')->unsigned();
            $table->timestamps();

            $table->foreign('attorney_state_id')->references('id')->on('attorney_has_states');
            $table->foreign('county_id')->references('id')->on('counties');
        });
    }

    public function down()
    {
        Schema::dropIfExists('attorney_has_counties');
    }
}
