<?php

namespace Database\Seeders;

use App\Models\State;
use Illuminate\Database\Seeder;

class StateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        State::create(['country_id' => '1', 'name' => 'California', 'abbr' => 'CA']);
        State::create(['country_id' => 1, 'name' => 'Illinois', 'abbr' => 'IL']);
        State::create(['country_id' => 1, 'name' => 'Texas', 'abbr' => 'TX']);
        State::create(['country_id' => 1, 'name' => 'Arizona', 'abbr' => 'AZ']);
        State::create(['country_id' => 1, 'name' => 'Florida', 'abbr' => 'FL']);
        State::create(['country_id' => 1, 'name' => 'New York', 'abbr' => 'NY']);
        State::create(['country_id' => 1, 'name' => 'Washington', 'abbr' => 'WA']);
        State::create(['country_id' => 1, 'name' => 'Nevada', 'abbr' => 'NV']);
        State::create(['country_id' => 1, 'name' => 'Michigan', 'abbr' => 'MI']);
        State::create(['country_id' => 1, 'name' => 'Massachusetts', 'abbr' => 'MA']);
        State::create(['country_id' => 1, 'name' => 'Pennsylvania', 'abbr' => 'PA']);
        State::create(['country_id' => 1, 'name' => 'Ohio', 'abbr' => 'OH']);
        State::create(['country_id' => 1, 'name' => 'Minnesota', 'abbr' => 'MN']);
        State::create(['country_id' => 1, 'name' => 'Virginia', 'abbr' => 'VA']);
        State::create(['country_id' => 1, 'name' => 'Utah', 'abbr' => 'UT']);
        State::create(['country_id' => 1, 'name' => 'North Carolina', 'abbr' => 'NC']);
        State::create(['country_id' => 1, 'name' => 'Maryland', 'abbr' => 'MD']);
        State::create(['country_id' => 1, 'name' => 'Georgia', 'abbr' => 'GA']);
        State::create(['country_id' => 1, 'name' => 'Missouri', 'abbr' => 'MO']);
        State::create(['country_id' => 1, 'name' => 'Hawaii', 'abbr' => 'HI']);
        State::create(['country_id' => 1, 'name' => 'Indiana', 'abbr' => 'IN']);
        State::create(['country_id' => 1, 'name' => 'Wisconsin', 'abbr' => 'WI']);
        State::create(['country_id' => 1, 'name' => 'Connecticut', 'abbr' => 'CT']);
        State::create(['country_id' => 1, 'name' => 'Tennessee', 'abbr' => 'TN']);
        State::create(['country_id' => 1, 'name' => 'New Jersey', 'abbr' => 'NJ']);
        State::create(['country_id' => 1, 'name' => 'Oregon', 'abbr' => 'OR']);
        State::create(['country_id' => 1, 'name' => 'Oklahoma', 'abbr' => 'OK']);
        State::create(['country_id' => 1, 'name' => 'Kentucky', 'abbr' => 'KY']);
        State::create(['country_id' => 1, 'name' => 'Colorado', 'abbr' => 'CO']);
        State::create(['country_id' => 1, 'name' => 'District of Columbia', 'abbr' => 'DC']);
        State::create(['country_id' => 1, 'name' => 'New Mexico', 'abbr' => 'NM']);
        State::create(['country_id' => 1, 'name' => 'Alabama', 'abbr' => 'AL']);
        State::create(['country_id' => 1, 'name' => 'Rhode Island', 'abbr' => 'RI']);
        State::create(['country_id' => 1, 'name' => 'Kansas', 'abbr' => 'KS']);
        State::create(['country_id' => 1, 'name' => 'Nebraska', 'abbr' => 'NE']);
        State::create(['country_id' => 1, 'name' => 'Delaware', 'abbr' => 'DE']);
        State::create(['country_id' => 1, 'name' => 'South Carolina', 'abbr' => 'SC']);
        State::create(['country_id' => 1, 'name' => 'Iowa', 'abbr' => 'IA']);
        State::create(['country_id' => 1, 'name' => 'Idaho', 'abbr' => 'ID']);
        State::create(['country_id' => 1, 'name' => 'Louisiana', 'abbr' => 'LA']);
        State::create(['country_id' => 1, 'name' => 'New Hampshire', 'abbr' => 'NH']);
        State::create(['country_id' => 1, 'name' => 'Arkansas', 'abbr' => 'AR']);
        State::create(['country_id' => 1, 'name' => 'Alaska', 'abbr' => 'AK']);
        State::create(['country_id' => 1, 'name' => 'Maine', 'abbr' => 'ME']);
        State::create(['country_id' => 1, 'name' => 'Mississippi', 'abbr' => 'MS']);
        State::create(['country_id' => 1, 'name' => 'South Dakota', 'abbr' => 'SD']);
        State::create(['country_id' => 1, 'name' => 'West Virginia', 'abbr' => 'WV']);
        State::create(['country_id' => 1, 'name' => 'North Dakota', 'abbr' => 'ND']);
        State::create(['country_id' => 1, 'name' => 'Vermont', 'abbr' => 'VT']);
        State::create(['country_id' => 1, 'name' => 'Montana', 'abbr' => 'MT']);
        State::create(['country_id' => 1, 'name' => 'Wyoming', 'abbr' => 'WY']);

    }

}



