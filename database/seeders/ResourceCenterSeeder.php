<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CleClass;
use App\Models\Judge;
use App\Models\JudgeReview;
use App\Models\Interpreter;
use App\Models\TranslationService;
use App\Models\Paralegal;
use App\Models\Bondsman;
use App\Models\ForumCategory;
use App\Models\ForumPost;
use App\Models\User;
use Carbon\Carbon;

class ResourceCenterSeeder extends Seeder
{
    public function run(): void
    {
        $this->seedCLEClasses();
        $this->seedJudges();
        $this->seedInterpreters();
        $this->seedTranslationServices();
        $this->seedParalegals();
        $this->seedBondsmen();
        $this->seedForumData();
    }

    private function seedCLEClasses(): void
    {
        $classes = [
            [
                'title' => 'Ethics in Legal Practice',
                'description' => 'Comprehensive overview of legal ethics and professional responsibility',
                'instructor' => 'Prof. <PERSON>',
                'date' => Carbon::now()->addDays(15),
                'time' => '09:00',
                'duration' => 3,
                'location' => 'Downtown Conference Center',
                'type' => 'in-person',
                'credits' => 3,
                'capacity' => 50,
                'enrolled' => 42,
                'status' => 'scheduled',
                'price' => 150.00,
                'category' => 'Ethics',
            ],
            [
                'title' => 'Digital Evidence in Court',
                'description' => 'Modern approaches to handling digital evidence in legal proceedings',
                'instructor' => 'Dr. Michael Chen',
                'date' => Carbon::now()->addDays(20),
                'time' => '14:00',
                'duration' => 2,
                'location' => 'Online Platform',
                'type' => 'online',
                'credits' => 2,
                'capacity' => 100,
                'enrolled' => 78,
                'status' => 'scheduled',
                'price' => 100.00,
                'category' => 'Technology',
                'meeting_url' => 'https://zoom.us/j/123456789',
            ],
            [
                'title' => 'Contract Law Updates 2024',
                'description' => 'Recent changes and updates in contract law',
                'instructor' => 'Judge Patricia Williams',
                'date' => Carbon::now()->subDays(5),
                'time' => '10:00',
                'duration' => 4,
                'location' => 'Law School Auditorium',
                'type' => 'hybrid',
                'credits' => 4,
                'capacity' => 75,
                'enrolled' => 75,
                'status' => 'completed',
                'price' => 200.00,
                'category' => 'Contract Law',
            ],
        ];

        foreach ($classes as $class) {
            CleClass::create($class);
        }
    }

    private function seedJudges(): void
    {
        $judges = [
            [
                'name' => 'Hon. Sarah Mitchell',
                'court' => 'Superior Court',
                'jurisdiction' => 'Los Angeles County',
                'position' => 'Presiding Judge',
                'bio' => 'Judge Mitchell has over 15 years of experience in civil and criminal law.',
                'average_rating' => 4.2,
                'total_ratings' => 156,
                'category_ratings' => [
                    'fairness' => 4.3,
                    'knowledge' => 4.5,
                    'temperament' => 3.8,
                    'efficiency' => 4.1,
                    'communication' => 4.0
                ],
                'status' => 'active',
                'specializations' => ['Civil Law', 'Criminal Law', 'Family Law'],
            ],
            [
                'name' => 'Hon. Robert Chen',
                'court' => 'District Court',
                'jurisdiction' => 'San Francisco County',
                'position' => 'District Judge',
                'bio' => 'Judge Chen specializes in corporate and intellectual property law.',
                'average_rating' => 3.8,
                'total_ratings' => 89,
                'category_ratings' => [
                    'fairness' => 4.0,
                    'knowledge' => 4.2,
                    'temperament' => 3.2,
                    'efficiency' => 3.8,
                    'communication' => 3.6
                ],
                'status' => 'under_review',
                'specializations' => ['Corporate Law', 'Intellectual Property'],
            ],
        ];

        foreach ($judges as $judgeData) {
            Judge::create($judgeData);
        }
    }

    private function seedInterpreters(): void
    {
        $interpreters = [
            [
                'name' => 'Maria Rodriguez',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'city' => 'Los Angeles',
                'state' => 'CA',
                'languages' => ['Spanish', 'English', 'Portuguese'],
                'specializations' => ['Legal', 'Medical', 'Business'],
                'certifications' => ['Court Certified', 'ATA Certified'],
                'availability' => 'available',
                'rating' => 4.8,
                'total_jobs' => 156,
                'hourly_rate' => 85.00,
                'experience_years' => 8,
                'status' => 'active',
                'joined_date' => Carbon::now()->subYears(4),
                'last_active' => Carbon::now()->subHours(2),
            ],
            [
                'name' => 'Chen Wei',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'city' => 'San Francisco',
                'state' => 'CA',
                'languages' => ['Mandarin', 'English', 'Cantonese'],
                'specializations' => ['Legal', 'Technical', 'Financial'],
                'certifications' => ['Court Certified', 'NAATI Certified'],
                'availability' => 'busy',
                'rating' => 4.6,
                'total_jobs' => 89,
                'hourly_rate' => 90.00,
                'experience_years' => 6,
                'status' => 'active',
                'joined_date' => Carbon::now()->subYears(3),
                'last_active' => Carbon::now()->subHours(1),
            ],
        ];

        foreach ($interpreters as $interpreter) {
            Interpreter::create($interpreter);
        }
    }

    private function seedTranslationServices(): void
    {
        $services = [
            [
                'company_name' => 'Global Legal Translations',
                'contact_person' => 'Sarah Martinez',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'website' => 'www.globallegal.com',
                'city' => 'Los Angeles',
                'state' => 'CA',
                'languages' => ['Spanish', 'French', 'German', 'Italian'],
                'document_types' => ['Contracts', 'Court Documents', 'Depositions', 'Legal Briefs'],
                'specializations' => ['Legal', 'Medical', 'Technical'],
                'certifications' => ['ATA Certified', 'ISO 17100'],
                'turnaround_time' => '24-48 hours',
                'price_per_word' => 0.15,
                'price_per_page' => 25.00,
                'rush_fee_multiplier' => 1.5,
                'rating' => 4.8,
                'total_jobs' => 234,
                'status' => 'active',
                'joined_date' => Carbon::now()->subYears(4),
                'last_active' => Carbon::now()->subHours(2),
            ],
        ];

        foreach ($services as $service) {
            TranslationService::create($service);
        }
    }

    private function seedParalegals(): void
    {
        $paralegals = [
            [
                'name' => 'Jennifer Thompson',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'city' => 'Los Angeles',
                'state' => 'CA',
                'specializations' => ['Corporate Law', 'Contract Law', 'Litigation'],
                'experience_years' => 8,
                'education' => ['Paralegal Certificate - UCLA', 'Bachelor of Arts - CSUN'],
                'certifications' => ['NALA Certified', 'California Advanced Paralegal'],
                'availability' => 'available',
                'employment_type' => 'full-time',
                'hourly_rate' => 45.00,
                'rating' => 4.8,
                'total_jobs' => 156,
                'skills' => ['Legal Research', 'Document Drafting', 'Case Management', 'E-Discovery'],
                'status' => 'active',
                'joined_date' => Carbon::now()->subYears(4),
                'last_active' => Carbon::now()->subHours(2),
            ],
        ];

        foreach ($paralegals as $paralegal) {
            Paralegal::create($paralegal);
        }
    }

    private function seedBondsmen(): void
    {
        $bondsmen = [
            [
                'company_name' => 'Liberty Bail Bonds',
                'contact_person' => 'John Mitchell',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'emergency_phone' => '+****************',
                'website' => 'www.libertybail.com',
                'city' => 'Los Angeles',
                'state' => 'CA',
                'service_areas' => ['Los Angeles County', 'Orange County', 'Riverside County'],
                'bond_types' => ['Felony', 'Misdemeanor', 'Traffic', 'Immigration', 'Federal'],
                'minimum_bond' => 500.00,
                'maximum_bond' => 1000000.00,
                'fee_percentage' => 10.00,
                'availability' => '24/7',
                'rating' => 4.7,
                'total_bonds' => 1456,
                'license_number' => 'CA-BB-12345',
                'insurance_amount' => 5000000.00,
                'status' => 'active',
                'joined_date' => Carbon::now()->subYears(5),
                'last_active' => Carbon::now()->subHours(1),
            ],
        ];

        foreach ($bondsmen as $bondsman) {
            Bondsman::create($bondsman);
        }
    }

    private function seedForumData(): void
    {
        $categories = [
            [
                'name' => 'General Discussion',
                'slug' => 'general-discussion',
                'description' => 'General legal topics and discussions',
                'color' => 'blue',
                'icon' => 'MessageSquare',
                'sort_order' => 1,
                'post_count' => 45,
            ],
            [
                'name' => 'Case Law',
                'slug' => 'case-law',
                'description' => 'Recent case law discussions and analysis',
                'color' => 'purple',
                'icon' => 'Scale',
                'sort_order' => 2,
                'post_count' => 32,
            ],
            [
                'name' => 'Practice Tips',
                'slug' => 'practice-tips',
                'description' => 'Professional practice advice and tips',
                'color' => 'green',
                'icon' => 'Lightbulb',
                'sort_order' => 3,
                'post_count' => 28,
            ],
        ];

        foreach ($categories as $category) {
            ForumCategory::create($category);
        }
    }
}
