<?php

namespace Database\Seeders;

use App\Models\County;
use App\Models\State;
use G<PERSON>zy\LaravelMysqlSpatial\Types\LineString;
use Grimzy\LaravelMysqlSpatial\Types\Point;
use Grimzy\LaravelMysqlSpatial\Types\Polygon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CountySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $data = file_get_contents(public_path('county.json'));
            $polygon = json_decode($data, true);

            foreach ($polygon['features'] as $feature) {
                $name = $feature['properties']['NAME'];
                if ($feature['geometry']['type'] == "Polygon") {
                    if (State::where('name', $feature['properties']['STATE_NAME'])->first()) {
                        $state_id = State::where('name', $feature['properties']['STATE_NAME'])->first()->id;

                        foreach ($feature['geometry']['coordinates'][0] as $key => $coordinate) {

                            $cor[] = new Point($coordinate[1], $coordinate[0]);

                        }
                        $lineString = new LineString($cor);
                        $polygon = new Polygon([$lineString]);
                        $geometry = $polygon;
                        $cor = [];

                    }

                    County::updateOrCreate(
                        ['name' => $name],
                        ['name' => $name, 'state_id' => $state_id, 'geometry' => $geometry]);
                } elseif ($feature['geometry']['type'] == "MultiPolygon") {
                    if (State::where('name', $feature['properties']['STATE_NAME'])->first()) {
                        $state_id = State::where('name', $feature['properties']['STATE_NAME'])->first()->id;
                        foreach ($feature['geometry']['coordinates'][0] as $key => $coordinate) {
                            $multipolygonJson = json_encode($coordinate);
                            $bracketReplace1 = str_replace('[','(',$multipolygonJson);
                            $bracketReplace2 = str_replace(']',')',$bracketReplace1);
                            //preg_match("/\(([^\(\)]+)\)/", $bracketReplace, $matches);
                            $polygon ="ST_GeomFromText('POLYGON$bracketReplace2')";
                            dd(DB::insert ("INSERT INTO counties (`name`,`state_id`,`geometry`) VALUES ('A',$state_id,$polygon)"));
                        }


                    }
                }


            }


        } catch (\Exception $exception) {

        }
    }
}
