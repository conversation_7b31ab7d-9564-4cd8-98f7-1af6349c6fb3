<?php

namespace Database\Seeders;

use App\Models\Address;
use App\Models\Appointment;
use App\Models\Attorney\AttroneyHasStates;
use App\Models\Attorney\AttroneyHasPracticeArea;
use App\Models\Code;
use App\Models\Country;
use App\Models\County;
use App\Models\Inquiry;
use App\Models\InquiryHasPracticeArea;
use App\Models\Language;
use App\Models\Message;
use App\Models\News;
use App\Models\PracticeArea;
use App\Models\Proposal;
use App\Models\Rating;
use App\Models\State;
use App\Models\Support;
use App\Models\SupportCategory;
use App\Models\User;
use App\Models\UserLanguage;
use App\Models\WorkExperience;
use Illuminate\Database\Seeder;

class TestDatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database for testing.
     *
     * @return void
     */
    public function run()
    {
        // Create users
        $admin = User::factory()->admin()->create([
            'email' => '<EMAIL>',
        ]);

        $attorney = User::factory()->attorney()->create([
            'email' => '<EMAIL>',
        ]);

        $client = User::factory()->client()->create([
            'email' => '<EMAIL>',
        ]);

        // Create additional users
        User::factory()->attorney()->count(3)->create();
        User::factory()->client()->count(5)->create();

        // Create countries
        $country = Country::factory()->create([
            'name' => 'United States',
            'code' => 'US',
            'sorting' => 100,
        ]);

        // Create states
        $state = State::factory()->create([
            'name' => 'California',
            'abbr' => 'CA',
            'country_id' => $country->id,
        ]);

        State::factory()->count(5)->create([
            'country_id' => $country->id,
        ]);

        // Create counties
        $county = County::factory()->create([
            'name' => 'Los Angeles County',
            'state_id' => $state->id,
        ]);

        County::factory()->count(10)->create();

        // Create addresses
        Address::factory()->create([
            'user_id' => $attorney->id,
            'state_id' => $state->id,
            'country_id' => $country->id,
        ]);

        Address::factory()->create([
            'user_id' => $client->id,
            'state_id' => $state->id,
            'country_id' => $country->id,
        ]);

        // Create practice areas
        $practiceAreas = PracticeArea::factory()->count(10)->create();

        // Associate attorney with states and counties
        $attorneyState = AttroneyHasStates::factory()->create([
            'user_id' => $attorney->id,
            'state_id' => $state->id,
        ]);

        // Associate attorney with practice areas
        foreach ($practiceAreas->take(3) as $practiceArea) {
            AttroneyHasPracticeArea::factory()->create([
                'user_id' => $attorney->id,
                'practice_area_id' => $practiceArea->id,
            ]);
        }

        // Create work experiences for attorney
        WorkExperience::factory()->count(2)->create([
            'user_id' => $attorney->id,
        ]);

        // Create languages
        $languages = Language::factory()->count(5)->create();

        // Associate users with languages
        foreach ($languages->take(2) as $language) {
            UserLanguage::factory()->create([
                'user_id' => $attorney->id,
                'language_id' => $language->id,
            ]);

            UserLanguage::factory()->create([
                'user_id' => $client->id,
                'language_id' => $language->id,
            ]);
        }

        // Create inquiries
        $inquiries = [];
        for ($i = 0; $i < 5; $i++) {
            $inquiry = Inquiry::factory()->create([
                'user_id' => $client->id,
                'state_id' => $state->id,
                'county_id' => $county->id,
            ]);

            // Associate inquiry with practice areas
            $inquiry->practice_areas()->attach($practiceAreas->random(2)->pluck('id')->toArray());

            $inquiries[] = $inquiry;
        }

        // Create proposals
        foreach ($inquiries as $inquiry) {
            Proposal::factory()->create([
                'user_id' => $attorney->id,
                'inquiry_id' => $inquiry->id,
            ]);
        }

        // Create appointments
        Appointment::factory()->count(3)->create([
            'attorney_id' => $attorney->id,
            'client_id' => $client->id,
        ]);

        // Create ratings
        Rating::factory()->count(5)->create([
            'attorney_id' => $attorney->id,
        ]);

        // Create messages
        Message::factory()->count(10)->create([
            'from_user_id' => $client->id,
            'to_user_id' => $attorney->id,
        ]);

        Message::factory()->count(10)->create([
            'from_user_id' => $attorney->id,
            'to_user_id' => $client->id,
        ]);

        // Create news
        News::factory()->count(5)->create();

        // Create support categories
        $supportCategories = SupportCategory::factory()->count(5)->create();

        // Create support tickets
        foreach ($supportCategories as $category) {
            Support::factory()->create([
                'user_id' => $attorney->id,
                'support_category_id' => $category->id,
            ]);

            Support::factory()->create([
                'user_id' => $client->id,
                'support_category_id' => $category->id,
            ]);
        }

        // Create verification codes
        Code::factory()->accountVerification()->create([
            'user_id' => $attorney->id,
            'code' => '123456',
        ]);

        Code::factory()->passwordReset()->create([
            'user_id' => $client->id,
            'code' => '654321',
        ]);
    }
}
