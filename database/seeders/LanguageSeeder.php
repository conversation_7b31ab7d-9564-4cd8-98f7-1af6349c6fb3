<?php

namespace Database\Seeders;

use App\Models\Language;
use Illuminate\Database\Seeder;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Language::create(['name'=>'Afar']);
        Language::create(['name'=>'Abkhaz']);
        Language::create(['name'=>'Avestan']);
        Language::create(['name'=>'Afrikaans']);
        Language::create(['name'=>'Akan']);
        Language::create(['name'=>'Amharic']);
        Language::create(['name'=>'Aragonese']);
        Language::create(['name'=>'Arabic']);
        Language::create(['name'=>'Assamese']);
        Language::create(['name'=>'Avaric']);
        Language::create(['name'=>'Aymara']);
        Language::create(['name'=>'Azerbaijani']);
        Language::create(['name'=>'South Azerbaijani']);
        Language::create(['name'=>'Bashkir']);
        Language::create(['name'=>'Belarusian']);
        Language::create(['name'=>'Bulgarian']);
        Language::create(['name'=>'Bihari']);
        Language::create(['name'=>'Bislama']);
        Language::create(['name'=>'Bambara']);
        Language::create(['name'=>'Bengali; Bangla']);
        Language::create(['name'=>'Tibetan Standard, Tibetan, Central']);
        Language::create(['name'=>'Breton']);
        Language::create(['name'=>'Bosnian']);
        Language::create(['name'=>'Catalan; Valencian']);
        Language::create(['name'=>'Chechen']);
        Language::create(['name'=>'Chamorro']);
        Language::create(['name'=>'Corsican']);
        Language::create(['name'=>'Cree']);
        Language::create(['name'=>'Czech']);
        Language::create(['name'=>'Old Church Slavonic, Church Slavonic, Old Bulgarian']);
        Language::create(['name'=>'Chuvash']);
        Language::create(['name'=>'Welsh']);
        Language::create(['name'=>'Danish']);
        Language::create(['name'=>'German']);
        Language::create(['name'=>'Divehi; Dhivehi; Maldivian;']);
        Language::create(['name'=>'Dzongkha']);
        Language::create(['name'=>'Ewe']);
        Language::create(['name'=>'Greek, Modern']);
        Language::create(['name'=>'English']);
        Language::create(['name'=>'Esperanto']);
        Language::create(['name'=>'Spanish; Castilian']);
        Language::create(['name'=>'Estonian']);
        Language::create(['name'=>'Basque']);
        Language::create(['name'=>'Persian (Farsi)']);
        Language::create(['name'=>'Fula; Fulah; Pulaar; Pular']);
        Language::create(['name'=>'Finnish']);
        Language::create(['name'=>'Fijian']);
        Language::create(['name'=>'Faroese']);
        Language::create(['name'=>'French']);
        Language::create(['name'=>'Western Frisian']);
        Language::create(['name'=>'Irish']);
        Language::create(['name'=>'Scottish Gaelic; Gaelic']);
        Language::create(['name'=>'Galician']);
        Language::create(['name'=>'Guaraní']);
        Language::create(['name'=>'Gujarati']);
        Language::create(['name'=>'Manx']);
        Language::create(['name'=>'Hausa']);
        Language::create(['name'=>'Hebrew (modern)']);
        Language::create(['name'=>'Hindi']);
        Language::create(['name'=>'Hiri Motu']);
        Language::create(['name'=>'Croatian']);
        Language::create(['name'=>'Haitian; Haitian Creole']);
        Language::create(['name'=>'Hungarian']);
        Language::create(['name'=>'Armenian']);
        Language::create(['name'=>'Herero']);
        Language::create(['name'=>'Interlingua']);
        Language::create(['name'=>'Indonesian']);
        Language::create(['name'=>'Interlingue']);
        Language::create(['name'=>'Igbo']);
        Language::create(['name'=>'Nuosu']);
        Language::create(['name'=>'Inupiaq']);
        Language::create(['name'=>'Ido']);
        Language::create(['name'=>'Icelandic']);
        Language::create(['name'=>'Italian']);
        Language::create(['name'=>'Inuktitut']);
        Language::create(['name'=>'Japanese']);
        Language::create(['name'=>'Javanese']);
        Language::create(['name'=>'Georgian']);
        Language::create(['name'=>'Kongo']);
        Language::create(['name'=>'Kikuyu, Gikuyu']);
        Language::create(['name'=>'Kwanyama, Kuanyama']);
        Language::create(['name'=>'Kazakh']);
        Language::create(['name'=>'Kalaallisut, Greenlandic']);
        Language::create(['name'=>'Khmer']);
        Language::create(['name'=>'Kannada']);
        Language::create(['name'=>'Korean']);
        Language::create(['name'=>'Kanuri']);
        Language::create(['name'=>'Kashmiri']);
        Language::create(['name'=>'Kurdish']);
        Language::create(['name'=>'Komi']);
        Language::create(['name'=>'Cornish']);
        Language::create(['name'=>'Kyrgyz']);
        Language::create(['name'=>'Latin']);
        Language::create(['name'=>'Luxembourgish, Letzeburgesch']);
        Language::create(['name'=>'Ganda']);
        Language::create(['name'=>'Limburgish, Limburgan, Limburger']);
        Language::create(['name'=>'Lingala']);
        Language::create(['name'=>'Lao']);
        Language::create(['name'=>'Lithuanian']);
        Language::create(['name'=>'Luba-Katanga']);
        Language::create(['name'=>'Latvian']);
        Language::create(['name'=>'Malagasy']);
        Language::create(['name'=>'Marshallese']);
        Language::create(['name'=>'Māori']);
        Language::create(['name'=>'Macedonian']);
        Language::create(['name'=>'Malayalam']);
        Language::create(['name'=>'Mongolian']);
        Language::create(['name'=>'Marathi (Marāṭhī)']);
        Language::create(['name'=>'Malay']);
        Language::create(['name'=>'Maltese']);
        Language::create(['name'=>'Burmese']);
        Language::create(['name'=>'Nauru']);
        Language::create(['name'=>'Norwegian Bokmål']);
        Language::create(['name'=>'North Ndebele']);
        Language::create(['name'=>'Nepali']);
        Language::create(['name'=>'Ndonga']);
        Language::create(['name'=>'Dutch']);
        Language::create(['name'=>'Norwegian Nynorsk']);
        Language::create(['name'=>'Norwegian']);
        Language::create(['name'=>'South Ndebele']);
        Language::create(['name'=>'Navajo, Navaho']);
        Language::create(['name'=>'Chichewa; Chewa; Nyanja']);
        Language::create(['name'=>'Occitan']);
        Language::create(['name'=>'Ojibwe, Ojibwa']);
        Language::create(['name'=>'Oromo']);
        Language::create(['name'=>'Oriya']);
        Language::create(['name'=>'Ossetian, Ossetic']);
        Language::create(['name'=>'Panjabi, Punjabi']);
        Language::create(['name'=>'Pāli']);
        Language::create(['name'=>'Polish']);
        Language::create(['name'=>'Pashto, Pushto']);
        Language::create(['name'=>'Portuguese']);
        Language::create(['name'=>'Quechua']);
        Language::create(['name'=>'Romansh']);
        Language::create(['name'=>'Kirundi']);
        Language::create(['name'=>'Romanian']);
        Language::create(['name'=>'Russian']);
        Language::create(['name'=>'Kinyarwanda']);
        Language::create(['name'=>'Sanskrit (Saṁskṛta)']);
        Language::create(['name'=>'Sardinian']);
        Language::create(['name'=>'Sindhi']);
        Language::create(['name'=>'Northern Sami']);
        Language::create(['name'=>'Sango']);
        Language::create(['name'=>'Sinhala, Sinhalese']);
        Language::create(['name'=>'Slovak']);
        Language::create(['name'=>'Slovene']);
        Language::create(['name'=>'Samoan']);
        Language::create(['name'=>'Shona']);
        Language::create(['name'=>'Somali']);
        Language::create(['name'=>'Albanian']);
        Language::create(['name'=>'Serbian']);
        Language::create(['name'=>'Swati']);
        Language::create(['name'=>'Southern Sotho']);
        Language::create(['name'=>'Sundanese']);
        Language::create(['name'=>'Swedish']);
        Language::create(['name'=>'Swahili']);
        Language::create(['name'=>'Tamil']);
        Language::create(['name'=>'Telugu']);
        Language::create(['name'=>'Tajik']);
        Language::create(['name'=>'Thai']);
        Language::create(['name'=>'Tigrinya']);
        Language::create(['name'=>'Turkmen']);
        Language::create(['name'=>'Tagalog']);
        Language::create(['name'=>'Tswana']);
        Language::create(['name'=>'Tonga (Tonga Islands)']);
        Language::create(['name'=>'Turkish']);
        Language::create(['name'=>'Tsonga']);
        Language::create(['name'=>'Tatar']);
        Language::create(['name'=>'Twi']);
        Language::create(['name'=>'Tahitian']);
        Language::create(['name'=>'Uyghur, Uighur']);
        Language::create(['name'=>'Ukrainian']);
        Language::create(['name'=>'Urdu']);
        Language::create(['name'=>'Uzbek']);
        Language::create(['name'=>'Venda']);
        Language::create(['name'=>'Vietnamese']);
        Language::create(['name'=>'Volapük']);
        Language::create(['name'=>'Walloon']);
        Language::create(['name'=>'Wolof']);
        Language::create(['name'=>'Xhosa']);
        Language::create(['name'=>'Yiddish']);
        Language::create(['name'=>'Yoruba']);
        Language::create(['name'=>'Zhuang, Chuang']);
        Language::create(['name'=>'Chinese']);
        Language::create(['name'=>'Zulu']);
    }
}
