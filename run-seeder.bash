#!/bin/bash
# Bash script for running the test seeder

# Set environment to testing
export APP_ENV=testing

# Clear cache
echo "Clearing cache..."
php artisan config:clear
php artisan cache:clear

# Create the testing database if it doesn't exist
echo "Creating testing database if it doesn't exist..."
mysql -u root -e "CREATE DATABASE IF NOT EXISTS legalfiber_testing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run migrations
echo "Running migrations..."
php artisan migrate:fresh --env=testing

# Run the seeder
echo "Running seeder..."
php artisan db:seed --class="Database\Seeders\TestDatabaseSeeder" --env=testing

echo -e "\n=== Seeder Completed ===\n"
