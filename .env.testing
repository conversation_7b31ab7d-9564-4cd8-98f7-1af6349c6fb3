APP_NAME=LegalFiber
APP_ENV=testing
APP_KEY=base64:kiB4Wnh3jlHxINLbfOZ2CAOiY6CAtqZHV4jGbj+7mes=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

# OPTION 1: SQLite in-memory database (fastest, but doesn't match production exactly)
#DB_CONNECTION=sqlite
#DB_DATABASE=:memory:

# OPTION 2: SQLite file database (if you have issues with in-memory SQLite)
# DB_CONNECTION=sqlite
# DB_DATABASE=D:/path/to/your/project/database/testing.sqlite  # Windows path
# DB_DATABASE=/path/to/your/project/database/testing.sqlite    # Linux path

# OPTION 3: MySQL/MariaDB (matches production environment)
 DB_CONNECTION=mysql
 DB_HOST=127.0.0.1
 DB_PORT=3306
 DB_DATABASE=legalfiber_testing
 DB_USERNAME=root
 DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=array
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=array
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

ACCESS_TOKEN=testing-token

CLIENT_TOTAL_PROFILE_POINT=10
ATTORNEY_TOTAL_PROFILE_POINT=10
