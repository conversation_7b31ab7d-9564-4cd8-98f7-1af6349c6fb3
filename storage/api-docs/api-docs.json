{"openapi": "3.0.0", "info": {"title": "Legal Fiber Api", "version": "1.0"}, "paths": {"/api/register": {"post": {"tags": ["Register"], "summary": "User Register", "description": "User Register here", "operationId": "Register", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["name", "email", "password", "role"], "properties": {"name": {"type": "text"}, "email": {"type": "text"}, "password": {"type": "password"}, "password_confirmation": {"type": "password"}, "role": {"type": "text"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Register Successfully", "content": {"application/json": {"schema": {}}}}, "200": {"description": "Register Successfully", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/api/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "User Login", "description": "Login User Here", "operationId": "auth<PERSON><PERSON><PERSON>", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "email"}, "password": {"type": "password"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Login Successfully", "content": {"application/json": {"schema": {}}}}, "200": {"description": "Login Successfully", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/api/verify": {"get": {"tags": ["Verify"], "summary": "User Account Verify", "description": "User account verify", "operationId": "Verify", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["code", "type", "user_id"], "properties": {"code": {"type": "text"}, "type": {"type": "text"}, "user_id": {"type": "text"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Verified Successfully", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/api/change-password": {"post": {"tags": ["Change Password"], "summary": "Change Password", "description": "Change Password", "operationId": "Change Password", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["old_password", "new_password", "password"], "properties": {"old_password": {"type": "text"}, "new_password": {"type": "text"}, "password": {"type": "text"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Password Changed Successfully", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/api/client/rating": {"post": {"tags": ["Rating"], "summary": "User Rating", "description": "User Rating here", "operationId": "Rating", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["from_user_id", "to_user_id", "description", "rating"], "properties": {"from_user_id": {"type": "text"}, "to_user_id": {"type": "text"}, "description": {"type": "text"}, "rating": {"type": "text"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Rate User Successfully", "content": {"application/json": {"schema": {}}}}, "200": {"description": "Rate User Successfully", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}}, "security": {"passport": {"type": "oauth2", "description": "Laravel passport oauth2 security.", "in": "header", "scheme": "https", "flows": {"password": {"authorizationUrl": "http://localhost/oauth/authorize", "tokenUrl": "http://localhost/oauth/token", "refreshUrl": "http://localhost/token/refresh", "scopes": []}}}}}