@echo off
REM Windows batch script for running tests

REM Set environment to testing
set APP_ENV=testing

REM Clear cache
echo Clearing cache...
php artisan config:clear
php artisan cache:clear

REM Prepare the test database
echo Preparing test database...
php artisan migrate:fresh --env=testing
php artisan db:seed --class=Database\Seeders\TestDatabaseSeeder --env=testing

REM Run all tests
echo.
echo === Running All Tests ===
echo.
php artisan test --env=testing

REM Run specific test groups
echo.
echo === Running Auth Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Auth

echo.
echo === Running Attorney Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney

echo.
echo === Running Client Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Client

REM Run specific controller tests
echo.
echo === Running ClientInquiry Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/ClientInquiry/ClientInquiryControllerTest.php

echo.
echo === Running Auth Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Auth/AuthControllerTest.php

echo.
echo === Running News Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/News/NewsControllerTest.php

echo.
echo === Running Proposal Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/Proposal/ProposalControllerTest.php

echo.
echo === Running WorkExperience Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/WorkExperience/WorkExperienceControllerTest.php

echo.
echo === Running Address Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/Address/AddressControllerTest.php

echo.
echo === Running Language Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/Language/LanguageControllerTest.php

echo.
echo === Running PracticeArea Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/PracticeAreaControllerTest.php

echo.
echo === Running Appointment Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/Appointment/AppointmentControllerTest.php

echo.
echo === Running Rating Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/Rating/RatingControllerTest.php

echo.
echo === Running UserProfile Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Attorney/UserProfile/UserProfileControllerTest.php

echo.
echo === Running Message Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Client/Message/MessageControllerTest.php

echo.
echo === Running ForgotPassword Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Auth/ForgotPasswordControllerTest.php

echo.
echo === Running ResetPassword Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Auth/ResetPasswordControllerTest.php

echo.
echo === Running SocialLogin Controller Tests ===
echo.
php artisan test --env=testing tests/Feature/Auth/SocialLoginControllerTest.php

echo.
echo === All Tests Completed ===
echo.

pause
