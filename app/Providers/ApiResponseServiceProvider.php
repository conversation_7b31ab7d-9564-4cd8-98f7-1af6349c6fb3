<?php

namespace App\Providers;

use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\ServiceProvider;

class ApiResponseServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {

        Response::macro('success', function ($message = '', $code = 200) {
            return response()->json(new SuccessResource([
                'message' => $message,
                'status_code' => $code
            ]), $code);
        });

        Response::macro('successData', function ($data = [], $message = '', $code = 200) {
            $data = ['status_code' => $code, 'data' => $data];
            if($message) $data['message'] = $message;
            return response()->json(new SuccessResource($data), $code);
        });

        Response::macro('error', function ($message = '', $code = 400) {
            if ($message instanceof \Throwable) {
                $message = $message->getMessage();
                $code = in_array($message->getCode(), array_keys(\Illuminate\Http\Response::$statusTexts)) ? $message->getCode() : 400;
            }
            return response()->json(new ErrorResource([
                'message' => $message,
                'status_code' => $code
            ]),
                $code);
        });
        Response::macro('errorData', function ($data = [], $message = '', $code = 400) {
            if($message instanceof \Throwable) {
                $statusCode = $message->getCode();
                $message = $message->getMessage();
                $code = in_array($statusCode, array_keys(\Illuminate\Http\Response::$statusTexts)) ? $statusCode : 400;
            }
            return response()->json(new ErrorResource([
                    'message' => $message,
                    'status_code' => $code
                ]+$data),
                $code);
        });
    }
}
