<?php

namespace App\Providers;

use App\Http\Resources\SuccessResource;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use Illuminate\Http\Request;
use Laravel\Socialite\Facades\Socialite;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        require_once app_path('Helpers/Response/ResponseHelper.php');
        require_once app_path('Helpers/GetUserType.php');
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);
        Request::macro('getUserType', function () {
               return request()->route()->getName();
        });
//        $this->app->register(\SocialiteProviders\Manager\ServiceProvider::class);
//
//        Socialite::extend('apple', function ($app) {
//            $config = $app['config']['services.apple'];
//            return Socialite::buildProvider(\SocialiteProviders\Apple\Provider::class, $config);
//        });
    }
}
