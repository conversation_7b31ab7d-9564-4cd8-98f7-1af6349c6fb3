<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;

class Attachment extends Model
{
    use HasFactory;

    protected $hidden = ['created_at','updated_at', 'pivot','extension','file','directory'];

    protected $guarded = [];

    protected $appends = ['file_url'];

    public function inquiry()
    {
        return $this->belongsToMany('App\Models\Inquiry');
    }

    public function getFileUrlAttribute() {
        return self::s3Url($this->directory.$this->file);
    }


    static function s3Url($key)
    {
        $s3 = Storage::disk('s3');
        $client = $s3->getDriver()->getAdapter()->getClient();
        $bucket = Config::get('filesystems.disks.s3.bucket');
        $command = $client->getCommand('GetObject', [
            'Bucket' => $bucket,
            'Key' => urldecode($key)
        ]);
        $request = $client->createPresignedRequest($command, Carbon::now()->addHour(1));
        return (string)$request->getUri();
    }

}
