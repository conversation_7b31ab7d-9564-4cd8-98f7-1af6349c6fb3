<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Support extends Model
{
    use HasFactory;

    protected $fillable = [
      'title',
      'description',
      'is_resolved',
      'support_category_id',
      'user_id',
    ];


    public function scopeActive($query)
    {
        return $query->where('is_resolved', false);
    }

    public function scopeClosed($query)
    {
        return $query->where('is_resolved', true);
    }

    public function attachments(): BelongsToMany
    {
        return $this->belongsToMany(Attachment::class, SupportDocument::class, 'support_id', 'attachment_id')
            ->withTimestamps();
    }




}
