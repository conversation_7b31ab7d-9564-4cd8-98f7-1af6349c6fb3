<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Post extends Model
{
    use HasFactory;

    protected $fillable =[
      'content','title','anonymous_user_id','user_id',
    ];

    public function attachments(): BelongsToMany
    {
        return $this->belongsToMany(Attachment::class, PostHasAttachment::class, 'post_id', 'attachment_id')
            ->withTimestamps();
    }

    public function visits(): HasMany
    {
        return $this->hasMany(PostVisit::class);
    }

    public function likes(): HasMany
    {
        return $this->hasMany(PostLike::class);
    }

    public function replies(): HasMany
    {
        return $this->hasMany(PostReplies::class)->with('childReplies');
    }
}
