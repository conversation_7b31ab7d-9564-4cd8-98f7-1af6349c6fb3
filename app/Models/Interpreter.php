<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Interpreter extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'emergency_phone',
        'address',
        'city',
        'state',
        'zip_code',
        'languages',
        'specializations',
        'certifications',
        'availability',
        'rating',
        'total_jobs',
        'hourly_rate',
        'experience_years',
        'status',
        'joined_date',
        'last_active',
        'bio',
        'website',
        'linkedin',
        'service_areas',
        'travel_available',
        'travel_rate',
        'is_active'
    ];

    protected $casts = [
        'languages' => 'array',
        'specializations' => 'array',
        'certifications' => 'array',
        'service_areas' => 'array',
        'rating' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'travel_rate' => 'decimal:2',
        'joined_date' => 'date',
        'last_active' => 'datetime',
        'travel_available' => 'boolean',
        'is_active' => 'boolean'
    ];

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('availability', 'available');
    }

    public function scopeByLanguage(Builder $query, string $language): Builder
    {
        return $query->whereJsonContains('languages', $language);
    }

    public function scopeBySpecialization(Builder $query, string $specialization): Builder
    {
        return $query->whereJsonContains('specializations', $specialization);
    }

    public function scopeByLocation(Builder $query, string $city = null, string $state = null): Builder
    {
        if ($city) {
            $query->where('city', 'like', "%{$city}%");
        }
        if ($state) {
            $query->where('state', 'like', "%{$state}%");
        }
        return $query;
    }

    public function scopeHighRated(Builder $query, float $minRating = 4.0): Builder
    {
        return $query->where('rating', '>=', $minRating);
    }

    public function scopeExperienced(Builder $query, int $minYears = 5): Builder
    {
        return $query->where('experience_years', '>=', $minYears);
    }

    public function scopeWithinBudget(Builder $query, float $maxRate): Builder
    {
        return $query->where('hourly_rate', '<=', $maxRate);
    }

    // Accessors
    public function getFormattedRateAttribute(): string
    {
        return '$' . number_format($this->hourly_rate, 2) . '/hour';
    }

    public function getFormattedTravelRateAttribute(): string
    {
        if (!$this->travel_rate) return 'N/A';
        return '$' . number_format($this->travel_rate, 2) . '/hour';
    }

    public function getFormattedRatingAttribute(): string
    {
        return number_format($this->rating, 1) . '/5.0';
    }

    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->zip_code
        ]);
        return implode(', ', $parts);
    }

    public function getLanguageListAttribute(): string
    {
        return implode(', ', $this->languages ?? []);
    }

    public function getSpecializationListAttribute(): string
    {
        return implode(', ', $this->specializations ?? []);
    }

    public function getCertificationListAttribute(): string
    {
        return implode(', ', $this->certifications ?? []);
    }

    public function getAvailabilityStatusAttribute(): string
    {
        return match($this->availability) {
            'available' => 'Available Now',
            'busy' => 'Currently Busy',
            'unavailable' => 'Unavailable',
            default => 'Unknown'
        };
    }

    public function getAvailabilityColorAttribute(): string
    {
        return match($this->availability) {
            'available' => 'green',
            'busy' => 'yellow',
            'unavailable' => 'red',
            default => 'gray'
        };
    }

    // Methods
    public function updateAvailability(string $availability): bool
    {
        if (!in_array($availability, self::getAvailabilityOptions())) {
            return false;
        }

        $this->update([
            'availability' => $availability,
            'last_active' => now()
        ]);

        return true;
    }

    public function incrementJobCount(): void
    {
        $this->increment('total_jobs');
        $this->update(['last_active' => now()]);
    }

    public function updateRating(float $newRating): void
    {
        // This would typically be calculated from actual job ratings
        $this->update(['rating' => $newRating]);
    }

    public function isAvailableForBooking(): bool
    {
        return $this->is_active && 
               $this->status === 'active' && 
               $this->availability === 'available';
    }

    public function hasLanguage(string $language): bool
    {
        return in_array($language, $this->languages ?? []);
    }

    public function hasSpecialization(string $specialization): bool
    {
        return in_array($specialization, $this->specializations ?? []);
    }

    public function hasCertification(string $certification): bool
    {
        return in_array($certification, $this->certifications ?? []);
    }

    // Static methods
    public static function getAvailabilityOptions(): array
    {
        return ['available', 'busy', 'unavailable'];
    }

    public static function getStatusOptions(): array
    {
        return ['active', 'inactive', 'pending'];
    }

    public static function getLanguageOptions(): array
    {
        return [
            'Spanish', 'French', 'German', 'Italian', 'Portuguese',
            'Mandarin', 'Cantonese', 'Japanese', 'Korean', 'Vietnamese',
            'Arabic', 'Hebrew', 'Persian', 'Turkish', 'Russian',
            'Polish', 'Dutch', 'Swedish', 'Norwegian', 'Danish',
            'Hindi', 'Urdu', 'Bengali', 'Tamil', 'Telugu',
            'Tagalog', 'Thai', 'Indonesian', 'Malay', 'Swahili'
        ];
    }

    public static function getSpecializationOptions(): array
    {
        return [
            'Legal', 'Medical', 'Business', 'Technical', 'Financial',
            'Educational', 'Government', 'Immigration', 'Court',
            'Deposition', 'Conference', 'Document Review'
        ];
    }

    public static function getCertificationOptions(): array
    {
        return [
            'Court Certified',
            'ATA Certified',
            'NAATI Certified',
            'State Certified',
            'Federal Court Certified',
            'Medical Interpreter Certified',
            'Legal Interpreter Certified'
        ];
    }
}
