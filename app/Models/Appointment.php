<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Appointment extends Model
{
    use HasFactory;

    protected $hidden = ['created_at', 'updated_at'];
    protected $guarded = [];

    public function attorney(): BelongsTo
    {
        return $this->belongsTo(User::class, 'attorney_id');
    }
    public function client(): BelongsTo
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    public function setTimeAttribute(string $value)
    {
        $this->attributes['time'] = Carbon::parse($value)->format('Y-m-d H:i');
    }
}
