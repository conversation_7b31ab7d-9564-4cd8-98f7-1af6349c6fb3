<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class TranslationService extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_name',
        'contact_person',
        'email',
        'phone',
        'emergency_phone',
        'website',
        'address',
        'city',
        'state',
        'zip_code',
        'languages',
        'document_types',
        'specializations',
        'certifications',
        'turnaround_time',
        'price_per_word',
        'price_per_page',
        'rush_fee_multiplier',
        'rating',
        'total_jobs',
        'status',
        'joined_date',
        'last_active',
        'description',
        'service_areas',
        'notarization_available',
        'rush_service_available',
        'minimum_order',
        'insurance_amount',
        'license_number',
        'is_active'
    ];

    protected $casts = [
        'languages' => 'array',
        'document_types' => 'array',
        'specializations' => 'array',
        'certifications' => 'array',
        'service_areas' => 'array',
        'price_per_word' => 'decimal:4',
        'price_per_page' => 'decimal:2',
        'rush_fee_multiplier' => 'decimal:2',
        'rating' => 'decimal:2',
        'insurance_amount' => 'decimal:2',
        'joined_date' => 'date',
        'last_active' => 'datetime',
        'notarization_available' => 'boolean',
        'rush_service_available' => 'boolean',
        'is_active' => 'boolean'
    ];

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopeByLanguage(Builder $query, string $language): Builder
    {
        return $query->whereJsonContains('languages', $language);
    }

    public function scopeBySpecialization(Builder $query, string $specialization): Builder
    {
        return $query->whereJsonContains('specializations', $specialization);
    }

    public function scopeByDocumentType(Builder $query, string $documentType): Builder
    {
        return $query->whereJsonContains('document_types', $documentType);
    }

    public function scopeByLocation(Builder $query, string $city = null, string $state = null): Builder
    {
        if ($city) {
            $query->where('city', 'like', "%{$city}%");
        }
        if ($state) {
            $query->where('state', 'like', "%{$state}%");
        }
        return $query;
    }

    public function scopeHighRated(Builder $query, float $minRating = 4.0): Builder
    {
        return $query->where('rating', '>=', $minRating);
    }

    public function scopeWithinBudget(Builder $query, float $maxPricePerWord): Builder
    {
        return $query->where('price_per_word', '<=', $maxPricePerWord);
    }

    public function scopeWithRushService(Builder $query): Builder
    {
        return $query->where('rush_service_available', true);
    }

    public function scopeWithNotarization(Builder $query): Builder
    {
        return $query->where('notarization_available', true);
    }

    // Accessors
    public function getFormattedPricePerWordAttribute(): string
    {
        return '$' . number_format($this->price_per_word, 4) . '/word';
    }

    public function getFormattedPricePerPageAttribute(): string
    {
        return '$' . number_format($this->price_per_page, 2) . '/page';
    }

    public function getRushPricePerWordAttribute(): float
    {
        return $this->price_per_word * ($this->rush_fee_multiplier ?? 1.5);
    }

    public function getFormattedRushPriceAttribute(): string
    {
        return '$' . number_format($this->rush_price_per_word, 4) . '/word (rush)';
    }

    public function getFormattedRatingAttribute(): string
    {
        return number_format($this->rating, 1) . '/5.0';
    }

    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->zip_code
        ]);
        return implode(', ', $parts);
    }

    public function getLanguageListAttribute(): string
    {
        return implode(', ', $this->languages ?? []);
    }

    public function getDocumentTypeListAttribute(): string
    {
        return implode(', ', $this->document_types ?? []);
    }

    public function getSpecializationListAttribute(): string
    {
        return implode(', ', $this->specializations ?? []);
    }

    public function getCertificationListAttribute(): string
    {
        return implode(', ', $this->certifications ?? []);
    }

    public function getFormattedInsuranceAttribute(): string
    {
        if (!$this->insurance_amount) return 'N/A';
        return '$' . number_format($this->insurance_amount, 0);
    }

    // Methods
    public function hasLanguage(string $language): bool
    {
        return in_array($language, $this->languages ?? []);
    }

    public function hasDocumentType(string $documentType): bool
    {
        return in_array($documentType, $this->document_types ?? []);
    }

    public function hasSpecialization(string $specialization): bool
    {
        return in_array($specialization, $this->specializations ?? []);
    }

    public function hasCertification(string $certification): bool
    {
        return in_array($certification, $this->certifications ?? []);
    }

    public function canHandleRushJobs(): bool
    {
        return $this->rush_service_available && $this->is_active && $this->status === 'active';
    }

    public function canProvideNotarization(): bool
    {
        return $this->notarization_available && $this->is_active && $this->status === 'active';
    }

    public function calculatePrice(int $wordCount, bool $isRush = false): float
    {
        $basePrice = $wordCount * $this->price_per_word;
        
        if ($isRush && $this->rush_service_available) {
            $basePrice *= ($this->rush_fee_multiplier ?? 1.5);
        }

        return max($basePrice, $this->minimum_order ?? 0);
    }

    public function calculatePagePrice(int $pageCount, bool $isRush = false): float
    {
        $basePrice = $pageCount * $this->price_per_page;
        
        if ($isRush && $this->rush_service_available) {
            $basePrice *= ($this->rush_fee_multiplier ?? 1.5);
        }

        return max($basePrice, $this->minimum_order ?? 0);
    }

    public function incrementJobCount(): void
    {
        $this->increment('total_jobs');
        $this->update(['last_active' => now()]);
    }

    public function updateRating(float $newRating): void
    {
        // This would typically be calculated from actual job ratings
        $this->update(['rating' => $newRating]);
    }

    public function isAvailableForBooking(): bool
    {
        return $this->is_active && $this->status === 'active';
    }

    // Static methods
    public static function getStatusOptions(): array
    {
        return ['active', 'inactive', 'pending'];
    }
}
