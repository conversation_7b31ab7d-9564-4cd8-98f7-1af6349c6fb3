<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ForumPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'user_id',
        'title',
        'slug',
        'content',
        'tags',
        'status',
        'is_pinned',
        'is_locked',
        'views',
        'likes',
        'replies',
        'last_reply_at',
        'last_reply_by',
        'flag_reason',
        'reviewed_at',
        'reviewed_by',
        'attachments',
        'ip_address',
        'anonymous'
    ];

    protected $casts = [
        'tags' => 'array',
        'attachments' => 'array',
        'is_pinned' => 'boolean',
        'is_locked' => 'boolean',
        'anonymous' => 'boolean',
        'last_reply_at' => 'datetime',
        'reviewed_at' => 'datetime'
    ];

    // Relationships
    public function category(): BelongsTo
    {
        return $this->belongsTo(ForumCategory::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function lastReplyUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_reply_by');
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function replies(): HasMany
    {
        return $this->hasMany(ForumReply::class, 'post_id');
    }

    public function publishedReplies(): HasMany
    {
        return $this->hasMany(ForumReply::class, 'post_id')->where('status', 'published');
    }

    // Scopes
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('status', 'published');
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    public function scopeFlagged(Builder $query): Builder
    {
        return $query->where('status', 'flagged');
    }

    public function scopeArchived(Builder $query): Builder
    {
        return $query->where('status', 'archived');
    }

    public function scopePinned(Builder $query): Builder
    {
        return $query->where('is_pinned', true);
    }

    public function scopeLocked(Builder $query): Builder
    {
        return $query->where('is_locked', true);
    }

    public function scopeByCategory(Builder $query, int $categoryId): Builder
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    public function scopePopular(Builder $query, int $minViews = 100): Builder
    {
        return $query->where('views', '>=', $minViews);
    }

    public function scopeRecent(Builder $query, int $days = 7): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    public function scopeWithTag(Builder $query, string $tag): Builder
    {
        return $query->whereJsonContains('tags', $tag);
    }

    // Accessors
    public function getUrlAttribute(): string
    {
        return "/forum/post/{$this->slug}";
    }

    public function getExcerptAttribute(): string
    {
        return \Str::limit(strip_tags($this->content), 150);
    }

    public function getFormattedDateAttribute(): string
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'published' => 'green',
            'pending' => 'yellow',
            'flagged' => 'red',
            'archived' => 'gray',
            default => 'gray'
        };
    }

    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'published' => 'Published',
            'pending' => 'Pending Review',
            'flagged' => 'Flagged',
            'archived' => 'Archived',
            default => 'Unknown'
        };
    }

    public function getAuthorNameAttribute(): string
    {
        if ($this->anonymous) {
            return 'Anonymous';
        }

        return $this->user->name ?? 'Unknown User';
    }

    public function getTagListAttribute(): string
    {
        return implode(', ', $this->tags ?? []);
    }

    public function getReadTimeAttribute(): int
    {
        $wordCount = str_word_count(strip_tags($this->content));
        return max(1, ceil($wordCount / 200)); // Assuming 200 words per minute
    }

    // Methods
    public function incrementViews(): void
    {
        $this->increment('views');
    }

    public function incrementLikes(): void
    {
        $this->increment('likes');
    }

    public function decrementLikes(): void
    {
        if ($this->likes > 0) {
            $this->decrement('likes');
        }
    }

    public function updateReplyCount(): void
    {
        $replyCount = $this->publishedReplies()->count();
        $lastReply = $this->publishedReplies()->latest()->first();

        $this->update([
            'replies' => $replyCount,
            'last_reply_at' => $lastReply?->created_at,
            'last_reply_by' => $lastReply?->user_id
        ]);
    }

    public function pin(): void
    {
        $this->update(['is_pinned' => true]);
    }

    public function unpin(): void
    {
        $this->update(['is_pinned' => false]);
    }

    public function lock(): void
    {
        $this->update(['is_locked' => true]);
    }

    public function unlock(): void
    {
        $this->update(['is_locked' => false]);
    }

    public function approve($reviewer = null): void
    {
        $this->update([
            'status' => 'published',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewer?->id,
            'flag_reason' => null
        ]);
    }

    public function flag(string $reason, $reviewer = null): void
    {
        $this->update([
            'status' => 'flagged',
            'flag_reason' => $reason,
            'reviewed_at' => now(),
            'reviewed_by' => $reviewer?->id
        ]);
    }

    public function archive(): void
    {
        $this->update(['status' => 'archived']);
    }

    public function canBeRepliedTo(): bool
    {
        return $this->status === 'published' && !$this->is_locked;
    }

    public function canBeModerated(): bool
    {
        return in_array($this->status, ['pending', 'flagged']);
    }

    public function isPinned(): bool
    {
        return $this->is_pinned;
    }

    public function isLocked(): bool
    {
        return $this->is_locked;
    }

    public function isPublished(): bool
    {
        return $this->status === 'published';
    }

    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags ?? []);
    }

    // Static methods
    public static function getStatuses(): array
    {
        return ['published', 'pending', 'flagged', 'archived'];
    }

    // Route model binding
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    // Boot method to handle model events
    protected static function boot()
    {
        parent::boot();

        static::created(function ($post) {
            $post->category->incrementPostCount();
        });

        static::deleted(function ($post) {
            $post->category->decrementPostCount();
        });
    }
}
