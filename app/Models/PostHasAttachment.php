<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PostHasAttachment extends Model
{
    use HasFactory;

    protected $table = "post_has_attachments";
    const POST_DOCUMENT_DIR = 'post/';
    protected $appends = ['file_url'];

    public function getFileUrlAttribute() {

        return $this->file ? config('filesystems.disks.s3.url') . self::POST_DOCUMENT_DIR.$this->file : null;
    }
}
