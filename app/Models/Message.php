<?php

namespace App\Models;

use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Database\Eloquent\BroadcastsEvents;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;

class Message extends Model
{
    use HasFactory;
    use BroadcastsEvents;
    use SoftDeletes;

    protected $fillable = ['message','type', 'to_user_id', 'from_user_id', 'read_status', 'delivered','deleted_by','archive_by','inquiry_id','proposal_id'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }


    public function sender()
    {
        return $this->belongsTo(User::class,'from_user_id','id');
    }

    public function receiver()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

   /* public function getMessageAttribute($value)
    {
        return Crypt::decryptString($value);
    }*/

    /**
     * Set the user's password attribute.
     *
     * @param string $value
     * @return void
     */
   /* public function setMessageAttribute(string $value)
    {
        $this->attributes['message'] = Crypt::encryptString($value);
    }*/

    /* public function setMessageAttribute(string $value)
    {
        $this->attributes['message'] = Crypt::encryptString($value);
    }*/


}
