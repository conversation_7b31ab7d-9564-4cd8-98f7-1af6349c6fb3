<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Proposal extends Model
{
    use HasFactory;
    protected $hidden = ['updated_at', 'pivot'];

    protected $guarded = [];

    const PENDING = 'PENDING';
    const ACTIVE = 'ACTIVE';
    const CANCELLED = 'CANCELLED';
    const COMPLETED = 'COMPLETED';

    public function attorneyuser() : BelongsTo
    {
        return $this->belongsTo(User::class,'user_id','id')->where('user_type', User::ATTORNEY);
    }
    public function attachments(): BelongsToMany
    {
        return $this->belongsToMany(Attachment::class, ProposalDocument::class, 'proposal_id', 'attachment_id')
            ->withTimestamps();
    }
}
