<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Paralegal extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'zip_code',
        'specializations',
        'experience_years',
        'education',
        'certifications',
        'availability',
        'employment_type',
        'hourly_rate',
        'rating',
        'total_jobs',
        'skills',
        'status',
        'joined_date',
        'last_active',
        'bio',
        'resume_url',
        'linkedin',
        'software_proficiency',
        'remote_work_available',
        'travel_available',
        'languages_spoken',
        'bar_admission',
        'notary_public',
        'is_active'
    ];

    protected $casts = [
        'specializations' => 'array',
        'education' => 'array',
        'certifications' => 'array',
        'skills' => 'array',
        'software_proficiency' => 'array',
        'languages_spoken' => 'array',
        'hourly_rate' => 'decimal:2',
        'rating' => 'decimal:2',
        'joined_date' => 'date',
        'last_active' => 'datetime',
        'remote_work_available' => 'boolean',
        'travel_available' => 'boolean',
        'notary_public' => 'boolean',
        'is_active' => 'boolean'
    ];

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('availability', 'available');
    }

    public function scopeBySpecialization(Builder $query, string $specialization): Builder
    {
        return $query->whereJsonContains('specializations', $specialization);
    }

    public function scopeByEmploymentType(Builder $query, string $employmentType): Builder
    {
        return $query->where('employment_type', $employmentType);
    }

    public function scopeByLocation(Builder $query, string $city = null, string $state = null): Builder
    {
        if ($city) {
            $query->where('city', 'like', "%{$city}%");
        }
        if ($state) {
            $query->where('state', 'like', "%{$state}%");
        }
        return $query;
    }

    public function scopeHighRated(Builder $query, float $minRating = 4.0): Builder
    {
        return $query->where('rating', '>=', $minRating);
    }

    public function scopeExperienced(Builder $query, int $minYears = 5): Builder
    {
        return $query->where('experience_years', '>=', $minYears);
    }

    public function scopeWithinBudget(Builder $query, float $maxRate): Builder
    {
        return $query->where('hourly_rate', '<=', $maxRate);
    }

    public function scopeRemoteAvailable(Builder $query): Builder
    {
        return $query->where('remote_work_available', true);
    }

    public function scopeTravelAvailable(Builder $query): Builder
    {
        return $query->where('travel_available', true);
    }

    public function scopeNotaryPublic(Builder $query): Builder
    {
        return $query->where('notary_public', true);
    }

    public function scopeBySkill(Builder $query, string $skill): Builder
    {
        return $query->whereJsonContains('skills', $skill);
    }

    // Accessors
    public function getFormattedRateAttribute(): string
    {
        return '$' . number_format($this->hourly_rate, 2) . '/hour';
    }

    public function getFormattedRatingAttribute(): string
    {
        return number_format($this->rating, 1) . '/5.0';
    }

    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->zip_code
        ]);
        return implode(', ', $parts);
    }

    public function getSpecializationListAttribute(): string
    {
        return implode(', ', $this->specializations ?? []);
    }

    public function getEducationListAttribute(): string
    {
        return implode(', ', $this->education ?? []);
    }

    public function getCertificationListAttribute(): string
    {
        return implode(', ', $this->certifications ?? []);
    }

    public function getSkillListAttribute(): string
    {
        return implode(', ', $this->skills ?? []);
    }

    public function getLanguageListAttribute(): string
    {
        return implode(', ', $this->languages_spoken ?? []);
    }

    public function getAvailabilityStatusAttribute(): string
    {
        return match($this->availability) {
            'available' => 'Available Now',
            'busy' => 'Currently Busy',
            'unavailable' => 'Unavailable',
            default => 'Unknown'
        };
    }

    public function getAvailabilityColorAttribute(): string
    {
        return match($this->availability) {
            'available' => 'green',
            'busy' => 'yellow',
            'unavailable' => 'red',
            default => 'gray'
        };
    }

    public function getEmploymentTypeDisplayAttribute(): string
    {
        return match($this->employment_type) {
            'full-time' => 'Full-time',
            'part-time' => 'Part-time',
            'contract' => 'Contract',
            'freelance' => 'Freelance',
            default => ucfirst($this->employment_type)
        };
    }

    public function getExperienceLevelAttribute(): string
    {
        return match(true) {
            $this->experience_years >= 10 => 'Senior',
            $this->experience_years >= 5 => 'Experienced',
            $this->experience_years >= 2 => 'Mid-level',
            default => 'Entry-level'
        };
    }

    // Methods
    public function hasSpecialization(string $specialization): bool
    {
        return in_array($specialization, $this->specializations ?? []);
    }

    public function hasSkill(string $skill): bool
    {
        return in_array($skill, $this->skills ?? []);
    }

    public function hasCertification(string $certification): bool
    {
        return in_array($certification, $this->certifications ?? []);
    }

    public function speaksLanguage(string $language): bool
    {
        return in_array($language, $this->languages_spoken ?? []);
    }

    public function updateAvailability(string $availability): bool
    {
        if (!in_array($availability, self::getAvailabilityOptions())) {
            return false;
        }

        $this->update([
            'availability' => $availability,
            'last_active' => now()
        ]);

        return true;
    }

    public function incrementJobCount(): void
    {
        $this->increment('total_jobs');
        $this->update(['last_active' => now()]);
    }

    public function updateRating(float $newRating): void
    {
        // This would typically be calculated from actual job ratings
        $this->update(['rating' => $newRating]);
    }

    public function isAvailableForHire(): bool
    {
        return $this->is_active && 
               $this->status === 'active' && 
               $this->availability === 'available';
    }

    public function canWorkRemotely(): bool
    {
        return $this->remote_work_available;
    }

    public function canTravel(): bool
    {
        return $this->travel_available;
    }

    public function isNotaryPublic(): bool
    {
        return $this->notary_public;
    }

    public function hasBarAdmission(): bool
    {
        return !empty($this->bar_admission);
    }

    // Static methods
    public static function getAvailabilityOptions(): array
    {
        return ['available', 'busy', 'unavailable'];
    }

    public static function getEmploymentTypeOptions(): array
    {
        return ['full-time', 'part-time', 'contract', 'freelance'];
    }

    public static function getStatusOptions(): array
    {
        return ['active', 'inactive', 'pending'];
    }
}
