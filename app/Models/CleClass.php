<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class CleClass extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'instructor',
        'date',
        'time',
        'duration',
        'location',
        'type',
        'credits',
        'capacity',
        'enrolled',
        'status',
        'price',
        'category',
        'requirements',
        'meeting_url',
        'notes',
        'is_active'
    ];

    protected $casts = [
        'date' => 'date',
        'time' => 'datetime:H:i',
        'price' => 'decimal:2',
        'requirements' => 'array',
        'is_active' => 'boolean'
    ];

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->where('date', '>=', now()->toDateString())
                    ->where('status', 'scheduled');
    }

    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    // Accessors
    public function getAvailableSpotsAttribute(): int
    {
        return $this->capacity - $this->enrolled;
    }

    public function getEnrollmentPercentageAttribute(): float
    {
        if ($this->capacity == 0) return 0;
        return round(($this->enrolled / $this->capacity) * 100, 2);
    }

    public function getIsFullAttribute(): bool
    {
        return $this->enrolled >= $this->capacity;
    }

    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    public function getFormattedDateTimeAttribute(): string
    {
        return $this->date->format('M j, Y') . ' at ' . $this->time->format('g:i A');
    }

    // Methods
    public function canEnroll(): bool
    {
        return $this->is_active && 
               $this->status === 'scheduled' && 
               !$this->is_full && 
               $this->date >= now()->toDateString();
    }

    public function incrementEnrollment(): bool
    {
        if (!$this->canEnroll()) {
            return false;
        }

        $this->increment('enrolled');
        return true;
    }

    public function decrementEnrollment(): bool
    {
        if ($this->enrolled > 0) {
            $this->decrement('enrolled');
            return true;
        }
        return false;
    }

    // Static methods
    public static function getTypes(): array
    {
        return ['online', 'in-person', 'hybrid'];
    }

    public static function getStatuses(): array
    {
        return ['scheduled', 'ongoing', 'completed', 'cancelled'];
    }

    public static function getCategories(): array
    {
        return [
            'Ethics',
            'Technology',
            'Contract Law',
            'Criminal Law',
            'Family Law',
            'Corporate Law',
            'Real Estate',
            'Immigration',
            'Intellectual Property',
            'Employment Law'
        ];
    }
}
