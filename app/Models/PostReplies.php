<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PostReplies extends Model
{
    use HasFactory;

    protected $fillable = ['post_id', 'user_id', 'content','parent_reply_id'];

    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function childReplies()
    {
        return $this->hasMany(PostReplies::class, 'parent_reply_id');
    }
}
