<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProposalDocument extends Model
{
    use HasFactory;

    protected $table = "proposal_has_attachments";

    //protected $hidden = ['created_at','updated_at', 'pivot'];
    const PROPOSAL_DIR = "proposal/";

    protected $appends = ['file_url'];

    public function getFileUrlAttribute() {

        return $this->file ? config('filesystems.disks.s3.url') . self::PROPOSAL_DIR.$this->file : null;
    }

}
