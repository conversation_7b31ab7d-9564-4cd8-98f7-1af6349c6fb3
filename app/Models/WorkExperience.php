<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class WorkExperience extends Model
{
    use HasFactory;

    protected $fillable = [
        'company',
        'position',
        'joined_year',
        'joined_month',
        'left_year',
        'left_month',
        'is_working',
        'user_id',
    ];

    protected $appends = ['experience_count', 'profile_points'];

    public function getExperienceCountAttribute()
    {
        $joinedDate = Carbon::parse($this->joined_year . '-' . $this->joined_month . '-1');
        $leftDate = Carbon::now();
        if ($this->is_working == 0) {
            $leftDate = Carbon::parse($this->left_year . '-' . $this->left_month . '-1');
        }
        $dateDiff = $joinedDate->diff($leftDate)->format('+%y years');
        return $dateDiff;
    }

    public function getProfilePointsAttribute()
    {
        return Auth::user()->profile_points;
    }
}
