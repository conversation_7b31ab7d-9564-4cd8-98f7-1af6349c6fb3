<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Auth;

class Inquiry extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'message',
        'user_id',
        'state_id',
        'county_id',
        'latitude',
        'longitude',
        'is_published',
    ];

    protected $appends = ['posted_at'];

    public function practice_areas(): BelongsToMany
    {
        return $this->belongsToMany(PracticeArea::class, InquiryHasPracticeArea::class,'inquiry_id','practice_area_id');
    }


    public function attachments(): BelongsToMany
    {
        return $this->belongsToMany(Attachment::class, InquiryHasAttachment::class, 'inquiry_id', 'attachment_id')
            ->withTimestamps();
    }

    public function proposal(): HasMany
    {
        return $this->hasMany(Proposal::class, 'inquiry_id', 'id');
    }

    public function activeproposal(): HasMany
    {
        return $this->hasMany(Proposal::class, 'inquiry_id', 'id')->where('status','active');
    }

    public function pendingproposal(): HasMany
    {
        return $this->hasMany(Proposal::class, 'inquiry_id', 'id')->where('status','pending');
    }

    public function completedproposal(): HasMany
    {
        return $this->hasMany(Proposal::class, 'inquiry_id', 'id')->where('status','completed');
    }

    public function attorneyProposal(): HasOne
    {
        return $this->hasOne(Proposal::class)->where('user_id', Auth::user()->id);
    }

    public function attorneyNotCancelledProposal(): HasOne
    {
        return $this->hasOne(Proposal::class)
            ->where('user_id', Auth::user()->id)
        ->where('status', 'cancelled');
    }

    public function dispensation(): HasOne
    {
        return $this->hasOne(Dispensation::class)->where('attorney_id', Auth::user()->id)
            ->where('status', '3');
    }

    public function dispose(): HasOne
    {
        return $this->hasOne(Dispensation::class)
            ->where('attorney_id', Auth::user()->id)->where('status', '2');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function client_address(): HasOne
    {
        return $this->hasOne(Address::class, 'user_id', 'user_id');
    }

    public function getPostedAtAttribute()
    {
        return $this->created_at->diffForHumans();
    }


//    public function attachment(): BelongsToMany
//    {
//        return $this->belongsToMany(Attachment::class, InquiryHasAttachment::class, 'inquiry_id', 'attachment_id')
//            ->withTimestamps();
//    }


}
