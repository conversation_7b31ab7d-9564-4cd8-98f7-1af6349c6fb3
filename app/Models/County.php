<?php

namespace App\Models;

use Grimzy\LaravelMysqlSpatial\Eloquent\SpatialTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class County extends Model
{
    use HasFactory;
    use SpatialTrait;
    protected $hidden= ['pivot'];
    protected $fillable = ['name','state_id','geometry'];
    protected $spatialFields = [
        'geometry'
    ];


}
