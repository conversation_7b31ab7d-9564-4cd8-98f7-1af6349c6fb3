<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserLanguage extends Model
{
    use HasFactory;

    protected $hidden = ['created_at','updated_at'];
    protected $fillable =
        [
            'user_id',
            'language_id'
        ];

    public function language()
    {
        return $this->belongsToMany('App\Models\Language','user_languages');
    }
}
