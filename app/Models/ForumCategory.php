<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ForumCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'icon',
        'sort_order',
        'post_count',
        'is_active',
        'requires_approval',
        'allowed_roles'
    ];

    protected $casts = [
        'allowed_roles' => 'array',
        'is_active' => 'boolean',
        'requires_approval' => 'boolean'
    ];

    // Relationships
    public function posts(): HasMany
    {
        return $this->hasMany(ForumPost::class, 'category_id');
    }

    public function publishedPosts(): HasMany
    {
        return $this->hasMany(ForumPost::class, 'category_id')->where('status', 'published');
    }

    public function pendingPosts(): HasMany
    {
        return $this->hasMany(ForumPost::class, 'category_id')->where('status', 'pending');
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order', 'asc');
    }

    public function scopeByColor(Builder $query, string $color): Builder
    {
        return $query->where('color', $color);
    }

    public function scopeWithPosts(Builder $query): Builder
    {
        return $query->where('post_count', '>', 0);
    }

    // Accessors
    public function getColorClassAttribute(): string
    {
        return match($this->color) {
            'blue' => 'bg-blue-100 text-blue-800',
            'green' => 'bg-green-100 text-green-800',
            'red' => 'bg-red-100 text-red-800',
            'yellow' => 'bg-yellow-100 text-yellow-800',
            'purple' => 'bg-purple-100 text-purple-800',
            'pink' => 'bg-pink-100 text-pink-800',
            'indigo' => 'bg-indigo-100 text-indigo-800',
            'gray' => 'bg-gray-100 text-gray-800',
            'orange' => 'bg-orange-100 text-orange-800',
            'teal' => 'bg-teal-100 text-teal-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    public function getUrlAttribute(): string
    {
        return "/forum/category/{$this->slug}";
    }

    public function getLatestPostAttribute(): ?ForumPost
    {
        return $this->publishedPosts()->latest()->first();
    }

    public function getActivePostCountAttribute(): int
    {
        return $this->publishedPosts()->count();
    }

    // Methods
    public function updatePostCount(): void
    {
        $this->update([
            'post_count' => $this->posts()->count()
        ]);
    }

    public function incrementPostCount(): void
    {
        $this->increment('post_count');
    }

    public function decrementPostCount(): void
    {
        if ($this->post_count > 0) {
            $this->decrement('post_count');
        }
    }

    public function canUserPost($user = null): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // If no allowed roles specified, anyone can post
        if (empty($this->allowed_roles)) {
            return true;
        }

        // Check if user has required role
        if (!$user) {
            return false;
        }

        // This would depend on your user role system
        // For now, assume all authenticated users can post
        return true;
    }

    public function requiresApproval(): bool
    {
        return $this->requires_approval;
    }

    // Static methods
    public static function getColorOptions(): array
    {
        return [
            'blue' => 'Blue',
            'green' => 'Green',
            'red' => 'Red',
            'yellow' => 'Yellow',
            'purple' => 'Purple',
            'pink' => 'Pink',
            'indigo' => 'Indigo',
            'gray' => 'Gray',
            'orange' => 'Orange',
            'teal' => 'Teal',
        ];
    }

    public static function getIconOptions(): array
    {
        return [
            'MessageSquare',
            'Scale',
            'Lightbulb',
            'Users',
            'BookOpen',
            'Briefcase',
            'Gavel',
            'FileText',
            'HelpCircle',
            'Settings',
            'Star',
            'Shield',
            'Award',
            'Target',
            'Zap'
        ];
    }

    // Route model binding
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
