<?php

namespace App\Models;

use App\Models\Attorney\AttroneyHasStates;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Laravel\Cashier\Billable;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;


class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, Billable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guard_name = 'api';
    protected $directory = 'avatar/';

    const ADMIN = 'ADMIN';
    const ATTORNEY = 'ATTORNEY';
    const CLIENT = 'CLIENT';
    const AVATAR_DIR = "avatar/";

    protected $fillable = [
        'name',
        'email',
        'phone_code',
        'phone',
        'password',
        'user_type',
        'avatar',
        'profile_points',
        'manual_verification',
        'fcm_token',
        'deviceToken',
        'consultation_fee'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'profile_points',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    protected $appends = ['profile_percentage'];

    public function providers()
    {
        return $this->hasMany(Provider::class, 'user_id', 'id');
    }

    public function user_profile()
    {
        return $this->belongsTo(Provider::class, 'user_id', 'id');
    }


    public function messages()
    {
        return $this->hasMany(Message::class, 'from_user_id', 'id');
    }

    public function support(): HasMany
    {
        return $this->hasMany(Support::class);
    }

    public function inquiry(): HasMany
    {
        return $this->hasMany(Inquiry::class);
    }

    public function post(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    public function address(): HasMany
    {
        return $this->hasMany(Address::class);
    }

    public function language(): HasMany
    {
        return $this->hasMany(UserLanguage::class);
    }

    public function profile(): HasOne
    {
        return $this->hasOne(UserProfile::class);
    }

    public function rating()
    {
        return $this->hasMany(Rating::class, 'from_user_id', 'id');
    }

    public function appointment() : HasMany
    {
        return $this->hasMany(Appointment::class, strtolower(getUserType()).'_id', 'id');
    }

    public function practiceArea(): BelongsToMany
    {
        return $this->belongsToMany(PracticeArea::class, 'attorney_has_legal_practice', 'user_id', 'practice_area_id')
            ->withTimestamps();
    }

    public function userLanguage(): BelongsToMany
    {
        return $this->belongsToMany(Language::class, UserLanguage::class, 'user_id', 'language_id')
            ->withTimestamps();
    }

    public function states(): BelongsToMany
    {
        return $this->belongsToMany(State::class, 'attorney_has_states', 'user_id', 'state_id')
            ->withTimestamps();
    }

    public function attorneyState(): HasMany
    {
        return $this->hasMany(AttroneyHasStates::class, 'user_id');
    }

        public function attorneyCounty(): HasMany
    {
        return $this->belongsToMany(
            County::class,
            'attorney_has_counties',
            'attorney_has_state_id',
            'county_id'
        )->join('attorney_has_states', 'attorney_has_states.id', '=', 'attorney_has_counties.attorney_has_state_id')
         ->whereColumn('attorney_has_states.attorney_id', 'attorneys.id');
    
    }

    public function dispensation(): HasMany
    {
        return $this->hasMany(Dispensation::class, 'attorney_id');
    }

    public function attorneyExperience(): HasMany
    {
        return $this->hasMany(WorkExperience::class, 'user_id');
    }

    public function attorneyProposal(): HasMany
    {
        return $this->hasMany(Proposal::class);
    }

    public function talkedTo():HasOne
    {
        return $this->hasOne(Message::class,'from_user_id')->latest();
    }

    public function relatedTo():HasOne
    {
        return $this->hasOne(Message::class,'to_user_id')->latest();
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function consultationWindow(): HasOne
    {
        return $this->hasOne(StripePayment::class, 'attorney_id')->where('client_id',Auth::user()->id)->latest();
    }




    public function getProfilePercentageAttribute()
    {
        $totalPoint = env('CLIENT_TOTAL_PROFILE_POINT');
        if ($this->user_type == self::ATTORNEY) {
            $totalPoint = env('ATTORNEY_TOTAL_PROFILE_POINT');
        }
        $count1 = $this->profile_points / $totalPoint;
        $count2 = $count1 * 100;
        $count = number_format($count2, 0);
        return $count;
    }


    public function getAvatarAttribute($value)
    {
// Remove all illegal characters from a url
        $url = filter_var($value, FILTER_SANITIZE_URL);
// Validate url
        if (filter_var($url, FILTER_VALIDATE_URL) !== false) {
            $url = $value;
        } else {
            $url = self::s3Url($this->directory . $value);
        }
        return $url;
    }

    static function s3Url($key)
    {
        $s3 = Storage::disk('s3');
        $client = $s3->getDriver()->getAdapter()->getClient();
        $bucket = Config::get('filesystems.disks.s3.bucket');
        $command = $client->getCommand('GetObject', [
            'Bucket' => $bucket,
            'Key' => urldecode($key)
        ]);
        $request = $client->createPresignedRequest($command, Carbon::now()->addHour(1));
        return (string)$request->getUri();
    }

}
