<?php

namespace App\Models;

use App\Models\Attorney\AttroneyHasStates;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class State extends Model
{
    use HasFactory;

    protected $hidden = ['created_at','updated_at', 'pivot'];

    /**
     * @return HasMany
     */
   public function counties(): HasMany
   {
       return $this->hasMany(County::class);
   }

   public function attorneys(): HasMany
   {
       return $this->hasMany(AttroneyHasStates::class, 'state_id');
   }
}
