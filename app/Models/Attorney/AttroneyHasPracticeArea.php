<?php

namespace App\Models\Attorney;

use App\Models\PracticeArea;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AttroneyHasPracticeArea extends Model
{
    use HasFactory;

    protected $table = "attorney_has_legal_practice";
    protected $guarded = [];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function practiceArea(): BelongsTo
    {
        return $this->belongsTo(PracticeArea::class, 'practice_area_id');
    }

}
