<?php

namespace App\Models\Attorney;

use App\Models\County;
use App\Models\PracticeArea;
use App\Models\State;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class AttroneyHasStates extends Model
{
    use HasFactory;

    protected $hidden = ['created_at', 'updated_at'];
    protected $table = "attorney_has_states";
    protected $guarded = [];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function county(): BelongsToMany
    {
        return $this->belongsToMany(County::class, 'attorney_has_counties', 'attorney_state_id', 'county_id')
            ->withTimestamps();
    }

    public function states():BelongsTo
    {
        return $this->belongsTo(State::class,'state_id','id');
    }
    
}
