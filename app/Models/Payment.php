<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'attorney_id',
        'stripe_payment_id',
        'proposal_id',
        'amount',
        'currency',
        'status',
    ];

    // Relationships
    public function attorney(): BelongsTo
    {
        return $this->belongsTo(User::class, 'attorney_id');
    }
    public function client(): BelongsTo
    {
        return $this->belongsTo(User::class, 'client_id');
    }
}
