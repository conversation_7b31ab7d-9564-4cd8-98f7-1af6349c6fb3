<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Judge extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'court',
        'jurisdiction',
        'position',
        'bio',
        'average_rating',
        'total_ratings',
        'category_ratings',
        'status',
        'photo_url',
        'specializations',
        'is_active'
    ];

    protected $casts = [
        'average_rating' => 'decimal:2',
        'category_ratings' => 'array',
        'specializations' => 'array',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function reviews(): HasMany
    {
        return $this->hasMany(JudgeReview::class);
    }

    public function approvedReviews(): HasMany
    {
        return $this->hasMany(JudgeReview::class)->where('status', 'approved');
    }

    public function pendingReviews(): HasMany
    {
        return $this->hasMany(JudgeReview::class)->where('status', 'pending');
    }

    public function flaggedReviews(): Has<PERSON><PERSON>
    {
        return $this->hasMany(JudgeReview::class)->where('status', 'flagged');
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    public function scopeByCourt(Builder $query, string $court): Builder
    {
        return $query->where('court', 'like', "%{$court}%");
    }

    public function scopeByJurisdiction(Builder $query, string $jurisdiction): Builder
    {
        return $query->where('jurisdiction', 'like', "%{$jurisdiction}%");
    }

    public function scopeHighRated(Builder $query, float $minRating = 4.0): Builder
    {
        return $query->where('average_rating', '>=', $minRating);
    }

    // Accessors
    public function getFormattedRatingAttribute(): string
    {
        return number_format($this->average_rating, 1);
    }

    public function getRatingStarsAttribute(): array
    {
        $rating = $this->average_rating;
        $stars = [];
        
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $rating) {
                $stars[] = 'full';
            } elseif ($i - 0.5 <= $rating) {
                $stars[] = 'half';
            } else {
                $stars[] = 'empty';
            }
        }
        
        return $stars;
    }

    public function getCategoryRatingAttribute(string $category): float
    {
        $ratings = $this->category_ratings ?? [];
        return $ratings[$category] ?? 0;
    }

    // Methods
    public function updateRatings(): void
    {
        $approvedReviews = $this->approvedReviews;
        $totalReviews = $approvedReviews->count();
        
        if ($totalReviews === 0) {
            $this->update([
                'average_rating' => 0,
                'total_ratings' => 0,
                'category_ratings' => []
            ]);
            return;
        }

        // Calculate overall average
        $overallAverage = $approvedReviews->avg('overall_rating');
        
        // Calculate category averages
        $categoryAverages = [];
        $categories = ['fairness', 'knowledge', 'temperament', 'efficiency', 'communication'];
        
        foreach ($categories as $category) {
            $categoryAverages[$category] = $approvedReviews->map(function ($review) use ($category) {
                $ratings = $review->category_ratings;
                return $ratings[$category] ?? 0;
            })->avg();
        }

        $this->update([
            'average_rating' => round($overallAverage, 2),
            'total_ratings' => $totalReviews,
            'category_ratings' => $categoryAverages
        ]);
    }

    public function canReceiveReviews(): bool
    {
        return $this->is_active && $this->status === 'active';
    }

    // Static methods
    public static function getStatuses(): array
    {
        return ['active', 'under_review', 'suspended'];
    }

    public static function getPositions(): array
    {
        return [
            'Chief Judge',
            'Associate Judge',
            'Presiding Judge',
            'Senior Judge',
            'Magistrate Judge',
            'Administrative Judge',
            'District Judge',
            'Circuit Judge',
            'Family Court Judge',
            'Criminal Court Judge',
            'Civil Court Judge'
        ];
    }

    public static function getSpecializations(): array
    {
        return [
            'Civil Law',
            'Criminal Law',
            'Family Law',
            'Corporate Law',
            'Real Estate Law',
            'Immigration Law',
            'Employment Law',
            'Intellectual Property',
            'Environmental Law',
            'Tax Law',
            'Bankruptcy Law',
            'Administrative Law'
        ];
    }
}
