<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InquiryHasAttachment extends Model
{
    use HasFactory;

    protected $table = "inquiry_has_attachments";
    const INQUIRY_DOCUMENT_DIR = 'inquiry/';
    protected $appends = ['file_url'];

    public function getFileUrlAttribute() {

        return $this->file ? config('filesystems.disks.s3.url') . self::INQUIRY_DOCUMENT_DIR.$this->file : null;
    }
}
