<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ForumReply extends Model
{
    use HasFactory;

    protected $fillable = [
        'post_id',
        'user_id',
        'parent_id',
        'content',
        'status',
        'likes',
        'flag_reason',
        'reviewed_at',
        'reviewed_by',
        'attachments',
        'ip_address',
        'anonymous'
    ];

    protected $casts = [
        'attachments' => 'array',
        'anonymous' => 'boolean',
        'reviewed_at' => 'datetime'
    ];

    // Relationships
    public function post(): BelongsTo
    {
        return $this->belongsTo(ForumPost::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(ForumReply::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(ForumReply::class, 'parent_id');
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    // Scopes
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('status', 'published');
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    public function scopeFlagged(Builder $query): Builder
    {
        return $query->where('status', 'flagged');
    }

    public function scopeDeleted(Builder $query): Builder
    {
        return $query->where('status', 'deleted');
    }

    public function scopeTopLevel(Builder $query): Builder
    {
        return $query->whereNull('parent_id');
    }

    public function scopeReplies(Builder $query): Builder
    {
        return $query->whereNotNull('parent_id');
    }

    public function scopeByPost(Builder $query, int $postId): Builder
    {
        return $query->where('post_id', $postId);
    }

    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    public function scopeRecent(Builder $query, int $days = 7): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    // Accessors
    public function getFormattedDateAttribute(): string
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'published' => 'green',
            'pending' => 'yellow',
            'flagged' => 'red',
            'deleted' => 'gray',
            default => 'gray'
        };
    }

    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'published' => 'Published',
            'pending' => 'Pending Review',
            'flagged' => 'Flagged',
            'deleted' => 'Deleted',
            default => 'Unknown'
        };
    }

    public function getAuthorNameAttribute(): string
    {
        if ($this->anonymous) {
            return 'Anonymous';
        }

        return $this->user->name ?? 'Unknown User';
    }

    public function getDepthAttribute(): int
    {
        $depth = 0;
        $parent = $this->parent;
        
        while ($parent) {
            $depth++;
            $parent = $parent->parent;
        }
        
        return $depth;
    }

    public function getIsNestedAttribute(): bool
    {
        return $this->parent_id !== null;
    }

    public function getHasChildrenAttribute(): bool
    {
        return $this->children()->exists();
    }

    // Methods
    public function incrementLikes(): void
    {
        $this->increment('likes');
    }

    public function decrementLikes(): void
    {
        if ($this->likes > 0) {
            $this->decrement('likes');
        }
    }

    public function approve($reviewer = null): void
    {
        $this->update([
            'status' => 'published',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewer?->id,
            'flag_reason' => null
        ]);

        // Update post reply count
        $this->post->updateReplyCount();
    }

    public function flag(string $reason, $reviewer = null): void
    {
        $this->update([
            'status' => 'flagged',
            'flag_reason' => $reason,
            'reviewed_at' => now(),
            'reviewed_by' => $reviewer?->id
        ]);
    }

    public function markAsDeleted(): void
    {
        $this->update(['status' => 'deleted']);
        
        // Update post reply count
        $this->post->updateReplyCount();
    }

    public function canBeModerated(): bool
    {
        return in_array($this->status, ['pending', 'flagged']);
    }

    public function isPublished(): bool
    {
        return $this->status === 'published';
    }

    public function isTopLevel(): bool
    {
        return $this->parent_id === null;
    }

    public function isReply(): bool
    {
        return $this->parent_id !== null;
    }

    public function canBeRepliedTo(): bool
    {
        return $this->status === 'published' && 
               $this->post->canBeRepliedTo() &&
               $this->depth < 3; // Limit nesting depth
    }

    // Static methods
    public static function getStatuses(): array
    {
        return ['published', 'pending', 'flagged', 'deleted'];
    }

    // Boot method to handle model events
    protected static function boot()
    {
        parent::boot();

        static::created(function ($reply) {
            if ($reply->status === 'published') {
                $reply->post->updateReplyCount();
            }
        });

        static::updated(function ($reply) {
            if ($reply->wasChanged('status')) {
                $reply->post->updateReplyCount();
            }
        });

        static::deleted(function ($reply) {
            $reply->post->updateReplyCount();
        });
    }
}
