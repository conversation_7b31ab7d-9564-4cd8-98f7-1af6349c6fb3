<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class PracticeArea extends Model
{
    use HasFactory;

    protected $hidden = ['created_at','updated_at', 'pivot'];

    public function inquiry()
    {
        return $this->belongsToMany('App\Models\Inquiry');
    }
    public function users() {
        return $this->belongsToMany(User::class, 'attorney_has_legal_practice', 'user_id', 'practice_area_id');
    }

    //for admin
    public function attorneys(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'attorney_has_legal_practice',  'practice_area_id','user_id');

    }

    /**
     * Scope a query to only include active practice areas.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to search practice areas by name or description.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }
}
