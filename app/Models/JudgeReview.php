<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JudgeReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'judge_id',
        'reviewer_hash',
        'overall_rating',
        'category_ratings',
        'comment',
        'case_type',
        'status',
        'flag_reason',
        'reviewed_at',
        'reviewed_by',
        'ip_address',
        'user_agent',
        'is_verified'
    ];

    protected $casts = [
        'category_ratings' => 'array',
        'reviewed_at' => 'datetime',
        'is_verified' => 'boolean'
    ];

    // Relationships
    public function judge(): BelongsTo
    {
        return $this->belongsTo(Judge::class);
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    // Scopes
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', 'approved');
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    public function scopeFlagged(Builder $query): Builder
    {
        return $query->where('status', 'flagged');
    }

    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('status', 'rejected');
    }

    public function scopeByJudge(Builder $query, int $judgeId): Builder
    {
        return $query->where('judge_id', $judgeId);
    }

    public function scopeByRating(Builder $query, int $rating): Builder
    {
        return $query->where('overall_rating', $rating);
    }

    public function scopeHighRating(Builder $query, int $minRating = 4): Builder
    {
        return $query->where('overall_rating', '>=', $minRating);
    }

    public function scopeLowRating(Builder $query, int $maxRating = 2): Builder
    {
        return $query->where('overall_rating', '<=', $maxRating);
    }

    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    // Accessors
    public function getFormattedRatingAttribute(): string
    {
        return $this->overall_rating . '/5 stars';
    }

    public function getRatingStarsAttribute(): array
    {
        $rating = $this->overall_rating;
        $stars = [];
        
        for ($i = 1; $i <= 5; $i++) {
            $stars[] = $i <= $rating ? 'full' : 'empty';
        }
        
        return $stars;
    }

    public function getCategoryRatingAttribute(string $category): int
    {
        $ratings = $this->category_ratings ?? [];
        return $ratings[$category] ?? 0;
    }

    public function getIsPositiveAttribute(): bool
    {
        return $this->overall_rating >= 4;
    }

    public function getIsNegativeAttribute(): bool
    {
        return $this->overall_rating <= 2;
    }

    public function getFormattedDateAttribute(): string
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    // Methods
    public function approve(User $reviewer = null): bool
    {
        $this->update([
            'status' => 'approved',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewer?->id,
            'flag_reason' => null
        ]);

        // Update judge ratings
        $this->judge->updateRatings();

        return true;
    }

    public function reject(User $reviewer = null, string $reason = null): bool
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewer?->id,
            'flag_reason' => $reason
        ]);

        return true;
    }

    public function flag(string $reason, User $reviewer = null): bool
    {
        $this->update([
            'status' => 'flagged',
            'flag_reason' => $reason,
            'reviewed_at' => now(),
            'reviewed_by' => $reviewer?->id
        ]);

        return true;
    }

    public function canBeModerated(): bool
    {
        return in_array($this->status, ['pending', 'flagged']);
    }

    // Static methods
    public static function getStatuses(): array
    {
        return ['approved', 'pending', 'flagged', 'rejected'];
    }

    public static function getCaseTypes(): array
    {
        return [
            'Civil',
            'Criminal',
            'Family',
            'Probate',
            'Traffic',
            'Small Claims',
            'Bankruptcy',
            'Immigration',
            'Administrative',
            'Appeals'
        ];
    }

    public static function getCategories(): array
    {
        return [
            'fairness' => 'Fairness & Impartiality',
            'knowledge' => 'Legal Knowledge',
            'temperament' => 'Judicial Temperament',
            'efficiency' => 'Court Efficiency',
            'communication' => 'Communication Skills'
        ];
    }

    public static function getFlagReasons(): array
    {
        return [
            'inappropriate_language' => 'Inappropriate Language',
            'personal_attack' => 'Personal Attack',
            'false_information' => 'False Information',
            'spam' => 'Spam Content',
            'bias' => 'Biased Review',
            'irrelevant' => 'Irrelevant Content',
            'duplicate' => 'Duplicate Review'
        ];
    }
}
