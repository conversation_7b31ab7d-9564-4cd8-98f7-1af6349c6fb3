<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Bondsman extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_name',
        'contact_person',
        'email',
        'phone',
        'emergency_phone',
        'website',
        'address',
        'city',
        'state',
        'zip_code',
        'service_areas',
        'bond_types',
        'minimum_bond',
        'maximum_bond',
        'fee_percentage',
        'availability',
        'rating',
        'total_bonds',
        'license_number',
        'insurance_amount',
        'status',
        'joined_date',
        'last_active',
        'description',
        'accepted_payment_methods',
        'collateral_accepted',
        'collateral_types',
        'business_hours',
        'mobile_service',
        'mobile_service_fee',
        'license_expiry',
        'bonding_company',
        'is_active'
    ];

    protected $casts = [
        'service_areas' => 'array',
        'bond_types' => 'array',
        'accepted_payment_methods' => 'array',
        'collateral_types' => 'array',
        'minimum_bond' => 'decimal:2',
        'maximum_bond' => 'decimal:2',
        'fee_percentage' => 'decimal:2',
        'rating' => 'decimal:2',
        'insurance_amount' => 'decimal:2',
        'mobile_service_fee' => 'decimal:2',
        'joined_date' => 'date',
        'last_active' => 'datetime',
        'license_expiry' => 'date',
        'collateral_accepted' => 'boolean',
        'mobile_service' => 'boolean',
        'is_active' => 'boolean'
    ];

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopeAvailable24_7(Builder $query): Builder
    {
        return $query->where('availability', '24/7');
    }

    public function scopeByServiceArea(Builder $query, string $serviceArea): Builder
    {
        return $query->whereJsonContains('service_areas', $serviceArea);
    }

    public function scopeByBondType(Builder $query, string $bondType): Builder
    {
        return $query->whereJsonContains('bond_types', $bondType);
    }

    public function scopeByLocation(Builder $query, string $city = null, string $state = null): Builder
    {
        if ($city) {
            $query->where('city', 'like', "%{$city}%");
        }
        if ($state) {
            $query->where('state', 'like', "%{$state}%");
        }
        return $query;
    }

    public function scopeHighRated(Builder $query, float $minRating = 4.0): Builder
    {
        return $query->where('rating', '>=', $minRating);
    }

    public function scopeCanHandleBond(Builder $query, float $bondAmount): Builder
    {
        return $query->where('minimum_bond', '<=', $bondAmount)
                    ->where('maximum_bond', '>=', $bondAmount);
    }

    public function scopeWithMobileService(Builder $query): Builder
    {
        return $query->where('mobile_service', true);
    }

    public function scopeAcceptsCollateral(Builder $query): Builder
    {
        return $query->where('collateral_accepted', true);
    }

    public function scopeLicenseValid(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('license_expiry')
              ->orWhere('license_expiry', '>', now());
        });
    }

    // Accessors
    public function getFormattedFeeAttribute(): string
    {
        return number_format($this->fee_percentage, 1) . '%';
    }

    public function getFormattedMinimumBondAttribute(): string
    {
        return '$' . number_format($this->minimum_bond, 0);
    }

    public function getFormattedMaximumBondAttribute(): string
    {
        return '$' . number_format($this->maximum_bond, 0);
    }

    public function getFormattedInsuranceAttribute(): string
    {
        return '$' . number_format($this->insurance_amount, 0);
    }

    public function getFormattedRatingAttribute(): string
    {
        return number_format($this->rating, 1) . '/5.0';
    }

    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->zip_code
        ]);
        return implode(', ', $parts);
    }

    public function getServiceAreaListAttribute(): string
    {
        return implode(', ', $this->service_areas ?? []);
    }

    public function getBondTypeListAttribute(): string
    {
        return implode(', ', $this->bond_types ?? []);
    }

    public function getPaymentMethodListAttribute(): string
    {
        return implode(', ', $this->accepted_payment_methods ?? []);
    }

    public function getCollateralTypeListAttribute(): string
    {
        return implode(', ', $this->collateral_types ?? []);
    }

    public function getAvailabilityDisplayAttribute(): string
    {
        return match($this->availability) {
            '24/7' => '24/7 Available',
            'business-hours' => 'Business Hours',
            'on-call' => 'On Call',
            default => 'Unknown'
        };
    }

    public function getAvailabilityColorAttribute(): string
    {
        return match($this->availability) {
            '24/7' => 'green',
            'business-hours' => 'blue',
            'on-call' => 'yellow',
            default => 'gray'
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => 'green',
            'inactive' => 'gray',
            'suspended' => 'red',
            default => 'gray'
        };
    }

    public function getLicenseStatusAttribute(): string
    {
        if (!$this->license_expiry) {
            return 'Valid';
        }

        $daysUntilExpiry = now()->diffInDays($this->license_expiry, false);
        
        if ($daysUntilExpiry < 0) {
            return 'Expired';
        } elseif ($daysUntilExpiry <= 30) {
            return 'Expiring Soon';
        } else {
            return 'Valid';
        }
    }

    public function getLicenseStatusColorAttribute(): string
    {
        return match($this->license_status) {
            'Valid' => 'green',
            'Expiring Soon' => 'yellow',
            'Expired' => 'red',
            default => 'gray'
        };
    }

    // Methods
    public function servesArea(string $area): bool
    {
        return in_array($area, $this->service_areas ?? []);
    }

    public function handlesBondType(string $bondType): bool
    {
        return in_array($bondType, $this->bond_types ?? []);
    }

    public function acceptsPaymentMethod(string $method): bool
    {
        return in_array($method, $this->accepted_payment_methods ?? []);
    }

    public function acceptsCollateralType(string $type): bool
    {
        return in_array($type, $this->collateral_types ?? []);
    }

    public function canHandleBond(float $bondAmount): bool
    {
        return $bondAmount >= $this->minimum_bond && 
               $bondAmount <= $this->maximum_bond &&
               $this->isAvailableForService();
    }

    public function calculateFee(float $bondAmount): float
    {
        if (!$this->canHandleBond($bondAmount)) {
            return 0;
        }

        return $bondAmount * ($this->fee_percentage / 100);
    }

    public function calculateTotalCost(float $bondAmount, bool $includeMobileService = false): float
    {
        $fee = $this->calculateFee($bondAmount);
        
        if ($includeMobileService && $this->mobile_service && $this->mobile_service_fee) {
            $fee += $this->mobile_service_fee;
        }

        return $fee;
    }

    public function incrementBondCount(): void
    {
        $this->increment('total_bonds');
        $this->update(['last_active' => now()]);
    }

    public function updateRating(float $newRating): void
    {
        // This would typically be calculated from actual bond ratings
        $this->update(['rating' => $newRating]);
    }

    public function isAvailableForService(): bool
    {
        return $this->is_active && 
               $this->status === 'active' && 
               $this->license_status === 'Valid';
    }

    public function isAvailable24_7(): bool
    {
        return $this->availability === '24/7';
    }

    public function offersMobileService(): bool
    {
        return $this->mobile_service;
    }

    public function acceptsCollateral(): bool
    {
        return $this->collateral_accepted;
    }

    public function hasValidLicense(): bool
    {
        return $this->license_status === 'Valid';
    }

    // Static methods
    public static function getAvailabilityOptions(): array
    {
        return ['24/7', 'business-hours', 'on-call'];
    }

    public static function getStatusOptions(): array
    {
        return ['active', 'inactive', 'suspended'];
    }
}
