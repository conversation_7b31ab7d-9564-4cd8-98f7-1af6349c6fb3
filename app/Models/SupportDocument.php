<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupportDocument extends Model
{
    protected $table = 'support_has_documents';

    const SUPPORT_DOCUMENT_DIR = 'support_document/';

    protected $hidden = ['created_at','updated_at', 'file'];

    protected $guarded = [];
    protected $appends = ['file_url'];

    public function getFileUrlAttribute() {

        return $this->file ? config('filesystems.disks.s3.url') . self::SUPPORT_DOCUMENT_DIR.$this->file : null;
    }
}
