<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;


class UserLog
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $userType = $request->route()->getName() ?: User::ADMIN;
       // $request->request->add(['access_user_type' => $userType]);
        return $next($request);
    }
}
