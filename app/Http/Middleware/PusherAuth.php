<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PusherAuth
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */

    public function handle($request, Closure $next)
    {
        $user = Auth::User();

        if ($user !== null) {
            if ($request->channel_name == "private-LegalChat." . $user->id) {
                return $next($request);
            } else {
                return response()->json(["message" => "Unauthenticated."], 401);
            }
        }

        return response()->json(["message" => "Unauthenticated."], 401);
    }

}
