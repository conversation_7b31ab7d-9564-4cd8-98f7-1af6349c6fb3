<?php

namespace App\Http\Resources\Client;

use Illuminate\Http\Resources\Json\JsonResource;

class InquiryHasPracticeAreaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'practice_area' => PracticeAreaResource::collection($this->whenLoaded('practice_area')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
