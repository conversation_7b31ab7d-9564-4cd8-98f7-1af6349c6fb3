<?php

namespace App\Http\Resources\Attorney;

use Illuminate\Http\Resources\Json\JsonResource;

class WorkExperienceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'company' => $this->company,
            'position' => $this->position,
            'joined_year' => $this->joined_year,
            'joined_month' => $this->joined_month,
            'left_year' => $this->left_year,
            'left_month' => $this->left_month,
            'is_working' => $this->is_working,
            'user_id' => $this->user_id,
            ];
    }
}
