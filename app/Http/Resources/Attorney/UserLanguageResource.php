<?php

namespace App\Http\Resources\Attorney;

use Illuminate\Http\Resources\Json\JsonResource;

class UserLanguageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
          'user_id'=>$this->user_id,
          'language_id'=>$this->language_id,
        ];
    }
}
