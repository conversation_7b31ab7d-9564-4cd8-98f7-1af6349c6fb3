<?php

namespace App\Http\Resources\Attorney;
use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

     public function toArray($request)
    {
        return [
            'from_user_id'=> User::find($this->from_user_id),
            'to_user_id'=>User::find($this->to_user_id),
            'message' => $this->message,
            'type' => $this->type,
            'read_status' => $this->read_status,
            'created_at' => $this->created_at
        ];
    }
}
