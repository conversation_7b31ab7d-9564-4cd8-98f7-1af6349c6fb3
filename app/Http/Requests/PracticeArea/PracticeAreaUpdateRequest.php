<?php

namespace App\Http\Requests\PracticeArea;

use App\Rules\StateCountyCheck;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class PracticeAreaUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'practice_areas' => "required|array",
            'practice_areas.*' => "required|exists:practice_areas,id",
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'practice_areas.*.exists' => "Some selected practice areas are invalid",
        ];
    }

    protected function failedValidation(Validator $validator)
    {

        $errorsList = (new ValidationException($validator))->errors();
        $errors = ['status' => 'error',
            'message' => array_values($errorsList)[0][0],
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
