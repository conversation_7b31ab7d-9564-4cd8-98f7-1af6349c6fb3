<?php

namespace App\Http\Requests\State;

use App\Rules\StateCountyCheck;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class StateAndCountyUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'state_details' => ["array","required", new StateCountyCheck()],
            'state_details.*.state_id' => "required|exists:states,id",
            /*'state_details.*.bar_date' => "required|date|date_format:Y-m-d",*/
            'state_details.*.counties.*' => ["required","exists:counties,id"],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'state.exists' => 'Selected state is invalid.',
            'state_details.*.counties.*.exists' => "Some selected counties are invalid.",
            'state_details.*.bay_date.date_format' => "Bay date should be in Y-m-d format.",
            'state_details.*.state_id' => "Some selected state is invalid",
        ];
    }

    protected function failedValidation(Validator $validator)
    {

        $errorsList = (new ValidationException($validator))->errors();
        $errors = ['status' => 'error',
            'message' => array_values($errorsList)[0][0],
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
