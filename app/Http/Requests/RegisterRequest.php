<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|min:2|max:255',
            'role' => 'required',
            'phone_code' => 'required',
            'email' => 'required|unique:users',
            'password' => 'required|min:6|max:25|confirmed',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errorsLists = (new ValidationException($validator))->errors();
        $errorsList = emailOrPhone($this->email) == "phone" ? str_replace('email','phone',array_values($errorsLists)[0][0]) : array_values($errorsLists)[0][0];

        $errors = ['status' => 'error',
            'message' => $errorsList,
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }

    public function messages(): array
    {
        return [
            'phone_code.required' => 'The country field is required',
        ];
    }
}
