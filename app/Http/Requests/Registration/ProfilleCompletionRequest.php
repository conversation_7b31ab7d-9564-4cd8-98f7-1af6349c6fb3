<?php

namespace App\Http\Requests\Registration;

use App\Rules\StateCountyCheck;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class ProfilleCompletionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'state_details' => ["required", new StateCountyCheck()],
            'practice_areas.*' => "required|exists:practice_areas,id",
            'state_details.*.state_id' => "required|exists:states,id",
            /*'state_details.*.bar_date' => "required|date|date_format:Y-m-d",*/
            //'state_details.*.counties.*' => "required|exists:counties,id",
            'state_details.*.counties.*' => ["required","exists:counties,id"],
            'state_id' => "required|exists:states,id",
            /*'bio' => "nullable",*/
            'business_name' => "nullable|string|max:255",
            'address_1' => "required|string|max:255",
            'address_2' => "nullable|string|max:255",
            'city' => "required |string|max:255",
            'zip_code' => "required|digits:5",
            'contact' => "nullable|digits:10",
            'website' => "nullable|string|max:255",
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'state.exists' => 'Selected state is invalid.',
            'contact.digits' => 'Contact number should be of 10 digits',
            'zip_code.digits' => 'Zip code should be of 5 digits',
            'state_details.*.counties.*.exists' => "Some selected counties are invalid.",
            'state_details.*.bay_date.date_format' => "Bay date should be in Y-m-d format.",
            'state_details.*.state_id' => "Some selected state is invalid",
        ];
    }

    protected function failedValidation(Validator $validator)
    {

        $errorsList = (new ValidationException($validator))->errors();
        $errors = ['status' => 'error',
            'message' => array_values($errorsList)[0][0],
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
