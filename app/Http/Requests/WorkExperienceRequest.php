<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class WorkExperienceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {    $sunFormat = "nullable";
        if ((int)$this->request->get('is_working') === 0) {
            $sunFormat ='|required|date_format:Y|after:joined_date';
        } else {
            $this->merge(['left_date'=> null]);
        }
        return [
            'company' => 'required|max:255',
            'position' => 'required|max:255',
            "joined_year" => "required|date_format:Y|before_or_equal:".date('Y'),
            "left_year" => "$sunFormat",
            "is_working" => "required|bool"
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'company.required' => 'Company is required.',
            'position.required' => 'Position is required.',
            'left_date.after' => 'Left date should be higher than joining date.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {

        $errorsList = (new ValidationException($validator))->errors();
        $errors = ['status' => 'error',
            'message' => array_values($errorsList)[0][0],
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
