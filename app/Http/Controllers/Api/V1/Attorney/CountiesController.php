<?php

namespace App\Http\Controllers\Api\V1\Attorney;

use App\Models\County;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class CountiesController
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request) : JsonResponse
    {
        $data = Cache::rememberForever('counties', function () {
            return County::orderBy('name')->get(['id', 'name', 'state_id']);
        });
        return successDataResponse($data);
    }
}
