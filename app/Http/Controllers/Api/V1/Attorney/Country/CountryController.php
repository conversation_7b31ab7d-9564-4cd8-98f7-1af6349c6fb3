<?php

namespace App\Http\Controllers\Api\V1\Attorney\Country;

use App\Http\Controllers\Controller;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

class CountryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = Cache::rememberForever('countries', function () {
            return Country::orderBy('sorting', 'desc')->get();
        });
        return successDataResponse($data);
    }


}
