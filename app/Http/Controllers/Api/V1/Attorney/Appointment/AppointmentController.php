<?php

namespace App\Http\Controllers\Api\V1\Attorney\Appointment;

use App\Http\Controllers\Controller;
use App\Http\Requests\AppointmentRequest;
use App\Http\Requests\RatingRequest;
use App\Http\Resources\Attorney\RatingResource;
use App\Models\Appointment;
use App\Models\Rating;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AppointmentController extends Controller
{

    public function index():JsonResponse
    {
        $timeZones = getTimeZones();
        return successDataResponse($timeZones);
    }
    public function store(AppointmentRequest $request): JsonResponse
    {
        try {
           /* if($this->checkAppointMent($request)) {
                return failedResponse('Pending appointment already exists');
            }*/
            $appointment = Auth::user()->appointment()->create($request->all());
            sendNotification("LegalFiber",trans('response.gotAppointment'),fcmToken($request->client_id),null,4,$request->client_id);
            return successDataResponse($appointment, trans('response.appointmentCreated'));
        } catch (\Exception $exception) {
            return failedResponse($exception->getMessage(), $exception->getCode());
        }
    }

    private function checkAppointMent(Request $request)
    {
        return Auth::user()->appointment()->where('client_id', $request->client_id)
            ->where('time','>', Carbon::now())->count();
    }
    public function get(Request $request,$participantID) {

        $data = Auth::user()->appointment()
            ->with('client:id,name,email,avatar')
            ->where('client_id', $participantID)
            ->get();

        return successDataResponse($data);
    }

    public function delete(Request $request,$appointmentId) : JsonResponse
    {
         if(Auth::user()->appointment()
            ->where('id', $appointmentId)
            ->delete()) {
             return successResponse(trans('response.appointmentRemoved'));
         }
         return failedResponse(trans('response.failedRemovingAppointment'));
    }
    public function approve(Request $request,$appointmentId) : JsonResponse
    {
        if(Auth::user()->appointment()
            ->where('id', $appointmentId)
            ->update(['is_approved' => 1])) {
            return successResponse(trans('response.appointmentApproved'));
        }
        return failedResponse(trans('response.failedApprovingAppointment'));
    }


}
