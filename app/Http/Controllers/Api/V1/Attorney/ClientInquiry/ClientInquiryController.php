<?php

namespace App\Http\Controllers\Api\V1\Attorney\ClientInquiry;

use App\Http\Controllers\Controller;
use App\Models\Inquiry;
use App\Traits\Attorney\UserRelatedID;
use App\Traits\Pagination;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ClientInquiryController extends Controller
{
    use Pagination;
    use UserRelatedID;

    public function index(Request $request): JsonResponse
    {
        $take = $request->page_size ?: 10;
        $data = $this->getInquiry(true)->paginate($take);
        $paginatedData = $this->paginatedData($data, $request);

        return successPaginationResponse($paginatedData);
    }

    /**
     * @param int $inquiryID
     * @return JsonResponse
     */
    public function get(int $inquiryID): JsonResponse
    {
        $data = $this->getInquiry(true)->where('id', $inquiryID)->first();
        return successDataResponse($data);
    }

    /**
     * @return Builder
     */
    private function getInquiry(bool $withAttachment = false): Builder
    {
        return Inquiry::query()
            ->whereHas('practice_areas', function ($query) {
                $query->whereIn('practice_area_id', $this->getPracticeArea());
            })

            ->with('practice_areas')
            ->with('dispensation')
            ->whereDoesntHave('dispose')

            ->has('client')
            ->with('client:id,name,email,avatar')
            /*->has('client_address')*/
            ->with('client_address')
            ->when($withAttachment, function ($query) {
                $query->with('attachments');
            })
            ->doesntHave('attorneyProposal')
            //->with('attorneyProposal:id,status,user_id,inquiry_id')
            ->whereIn('county_id', $this->getCountyIDs())
            ->orderBy('id', 'desc')
            ->select(['id', 'title', 'message', 'user_id', 'created_at']);
    }

    public function dispensation(Request $request)
    {
        $dispensation = Auth::user()->dispensation()->updateOrCreate(
            ['inquiry_id' => $request->inquiry_id],
            [
                'status' => $request->status
            ]);
        if ($request->status == "2") {
            return successResponse(trans('response.messageDisposed'));
        }
        if ($request->status == "3" ||$request->status == "0") {
            return successDataResponse($dispensation,trans('response.messagedMarkedConsidered'));
        }
    }


}
