<?php

namespace App\Http\Controllers\Api\V1\Attorney\Language;

use App\Http\Controllers\Controller;
use App\Http\Requests\Language\LanguageStoreRequest;
use App\Http\Resources\Attorney\LanguageResource;
use App\Http\Resources\Attorney\UserLanguageResource;
use App\Models\Language;
use App\Models\UserLanguage;
use App\Traits\ProfilePoints\ProfilePoints;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LanguageController extends Controller
{
    use ProfilePoints;
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $languages = Language::all();
        return successDataResponse(LanguageResource::make($languages));
    }
    /**
     * @param LanguageStoreRequest $request
     * @return JsonResponse
     */
    public function store(LanguageStoreRequest $request): JsonResponse
    {
        if($request->user()->userLanguage()->count() <= 0) {
            $this->addProfilePoints();
        }
        $request->user()->userLanguage()->sync($request->language_id);
        return successDataResponse(['profile_points'=> session('profile_points') ?? Auth::user()->profile_points],trans('response.languageAdded'));

    }

}
