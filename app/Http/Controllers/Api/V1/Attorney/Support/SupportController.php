<?php

namespace App\Http\Controllers\Api\V1\Attorney\Support;

use App\Http\Controllers\Controller;
use App\Http\Requests\Ticket\TicketStoreRequest;
use App\Http\Resources\Attorney\SupportResource;
use App\Models\Support;
use App\Models\SupportCategory;
use App\Models\SupportDocument;
use App\Traits\FileUpload\FileUpload;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SupportController extends Controller
{

    use FileUpload;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(): JsonResponse
    {
        $tickets = Auth::user()->support()->with(['attachments'])->get();
        $data = [
            'active_tickets' => $tickets->where('is_resolved', false)->toArray(),
            'closed_tickets' => $tickets->where('is_resolved', true)->toArray()
        ];
        return successDataResponse(SupportResource::make($data));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function category()
    {
        $supportCategory = SupportCategory::all();
        return response()->json(SupportResource::make($supportCategory), 200);
    }

    /**
     * @param TicketStoreRequest $request
     * @return JsonResponse
     */
    public function store(TicketStoreRequest $request): JsonResponse
    {
        try {
            $supportData = Auth::user()->support()->create($request->all());
           $supportDocs =  $this->handleSupportDocument($request, $supportData);
            return successDataResponse($supportData->load('attachments'));
        } catch (\Throwable $e) {
            return failedResponse($e);
        }
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(int $id): JsonResponse
    {
        if (Auth::user()->support()->where('id', $id)->delete()) {
            return successResponse(trans('response.ticketRemoved'));
        }
        return failedResponse(trans('response.failedRemovingTicket'));
    }

    /**
     * @param Request $request
     * @param Support $supportData
     */
    private function handleSupportDocument(Request $request, Support $supportData): void
    {
        if ($documents = $request->file('support_document')) {
            foreach ($documents as $document) {
                $fileName = $this->uploadToS3($document, SupportDocument::SUPPORT_DOCUMENT_DIR);
                if (!$fileName) continue;
                $attachmentID = $this->uploadAttachment($document, $fileName, SupportDocument::SUPPORT_DOCUMENT_DIR);
                $supportData->attachments()->attach([
                   $attachmentID
                ]);
            }
        }
    }
}
