<?php

namespace App\Http\Controllers\Api\V1\Attorney;

use App\Http\Requests\State\StateAndCountyUpdateRequest;
use App\Http\Requests\State\StateGetRequest;
use App\Models\State;
use App\Traits\UserDetailsUpdate\UserInfoUpdate;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class StateController
{
    use UserInfoUpdate;

    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = Cache::rememberForever('states', function () {
            return State::orderBy('name')->get(['id', 'name', 'abbr']);
        });
        return successDataResponse($data);
    }

    /**
     * @param StateGetRequest $request
     * @return JsonResponse
     */
    public function getCounties(StateGetRequest $request): JsonResponse
    {
        $stateIDs = explode(',', $request->state_id);
        $data = State::with(['counties' => function ($query) use ($request) {
            if ($request->search) {
                $query->where('name', 'LIKE', "%$request->search%");
            }
            $query->orderBy('name')->select(['id', 'name', 'state_id']);
        }])
            ->when($request->state_id, function ($query) use ($stateIDs, $request) {

                $query->whereIn('id', $stateIDs);
            })
            ->select(['id', 'name', 'abbr'])->paginate(10)
            ->appends(['state_id' => $request->state_id, 'search' => $request->search]);
        return successDataResponse($data);
    }

    /**
     * @param StateAndCountyUpdateRequest $request
     * @return JsonResponse
     */
    public function update(StateAndCountyUpdateRequest $request): JsonResponse
    {
        try {
            $this->addStates($request)->addCounties($request);
            $userData = Auth::user()->attorneyState()->with('county:id,name,state_id','states')->get();
            return successDataResponse($userData, trans('response.stateAndCountiesUpdated'));
        } catch (\Throwable $e) {
            return failedResponse(trans('response.failedUpdatingDetails'));
        }


    }
}
