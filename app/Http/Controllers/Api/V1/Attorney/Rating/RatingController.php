<?php

namespace App\Http\Controllers\Api\V1\Attorney\Rating;

use App\Http\Controllers\Controller;
use App\Http\Requests\RatingRequest;
use App\Http\Resources\Attorney\RatingResource;
use App\Models\Rating;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RatingController extends Controller
{
    public function store(RatingRequest $request)
    {

        try {
            if ($request->has('rating')) {
                $storeRating = Auth::user()->rating()->create($request->all());
                return successResponse(['data' => $storeRating], 201);
            }
        } catch (\Exception $exception) {
            return failedResponse($exception->getMessage(), $exception->getCode());
        }
    }



}
