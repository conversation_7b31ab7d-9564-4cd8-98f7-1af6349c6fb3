<?php

namespace App\Http\Controllers\Api\V1\Attorney\Proposal;

use App\Http\Controllers\Controller;
use App\Models\Inquiry;
use App\Models\Proposal;
use App\Models\ProposalDocument;
use App\Traits\Attorney\UserRelatedID;
use App\Traits\FileUpload\FileUpload;
use App\Traits\Pagination;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ProposalController extends Controller
{
    use FileUpload;
    use UserRelatedID;
    use Pagination;

    public function index(Request $request): JsonResponse
    {
        $status = $request->status ?: Proposal::PENDING;
        $take = $request->page_size ?: 10;
        $data = $this->getProposedInquiry(true)
            ->whereRelation('attorneyProposal', 'status', '=', $status)
            ->orderBy('id','desc')->paginate($take);
        $paginatedData = $this->paginatedData($data, $request);
        return successPaginationResponse($paginatedData);
    }

    public function get(int $id): JsonResponse
    {
        $data = $this->getProposedInquiry(true)->where('id', $id)->first();
        return successDataResponse($data);

    }

    public function store(Request $request): JsonResponse
    {
        try {
           $inquiry =  $this->isValidProposal($request->inquiry_id);
            $proposal = Auth::user()->attorneyProposal()
                ->create($request->except('proposal_document'));
            $this->handleProposalDocument($request, $proposal);
            sendNotification("LegalFiber",trans('response.gotProposalRequest'),fcmToken($inquiry->user_id),null,3);
            return successDataResponse(trans('response.proposalSubmitted'));
        } catch (\Throwable $e) {
            return failedResponse($e->getMessage());
        }
    }

    public function cancelProposal(int $id): JsonResponse
    {
        try {
            Auth::user()->attorneyProposal()->findOrFail($id)->delete();
            return successResponse(trans('response.proposalCancelled'));
        } catch (\Throwable $e) {
            return failedResponse(trans('response.failedCancellingProposal'));
        }
    }

    private function handleProposalDocument(Request $request, Proposal $proposalData): void
    {
        if ($documents = $request->file('proposal_document')) {
            foreach ($documents as $document) {
                $fileName = $this->uploadToS3($document, ProposalDocument::PROPOSAL_DIR);
                if (!$fileName) continue;
                $attachmentID = $this->uploadAttachment($document, $fileName, ProposalDocument::PROPOSAL_DIR);
                $proposalData->attachments()->attach([
                    $attachmentID
                ]);
            }
        }
    }

    private function isValidProposal(int $inquiryID): Inquiry
    {
        $data = Inquiry::query()
            ->with(['practice_areas' => function ($query) {
                return $query->whereIn('practice_area_id', $this->getPracticeArea());
            }])
            ->where('id', $inquiryID)
            ->with('attorneyProposal')
            ->whereIn('county_id', $this->getCountyIDs())->first();

        if (!$data instanceof Inquiry) {
            throw new \Exception(trans('response.cannotRequestProposalForThisInquiry'));
        }
        if ($data->attorneyProposal) {
            throw new \Exception(trans('response.alreadyProposalSent'));
        }

        return $data;
    }

    private function getProposedInquiry(bool $details = true): Builder
    {
        return Inquiry::query()
            ->with('practice_areas')
            ->with('client_address')
            ->with('client:id,name,email,avatar')
            ->has('attorneyProposal')
            ->when($details, function ($query) {
                $query->with('attachments')
                    ->with(['attorneyProposal' => function ($proposalQuery) {
                        $proposalQuery->select('id', 'status', 'user_id', 'inquiry_id', 'description','consultation_fee')
                            ->with('attorneyuser')
                            ->with('attachments');
                             }]);



//                    ->with('attorneyProposal:id,status,user_id,inquiry_id,description')
            })
            ->select(['id', 'title', 'message', 'user_id', 'created_at']);
    }

    public function complete($proposal_id): JsonResponse
    {
        Log::info('Proposal ID:'.$proposal_id);
        Proposal::where('id',$proposal_id)->update(['status'=>'completed']);
        return successResponse(trans('response.contractCompleted'));
    }
}
