<?php

namespace App\Http\Controllers\Api\V1\Attorney\Registration;

use App\Http\Requests\Registration\ProfilleCompletionRequest;
use App\Traits\ProfilePoints\ProfilePoints;
use App\Traits\UserDetailsUpdate\UserInfoUpdate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class RegistrationController
{
    use UserInfoUpdate;
    use ProfilePoints;
    /**
     * @param ProfilleCompletionRequest $
     * @return JsonResponse
     */
    public function index(ProfilleCompletionRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $this->createProfile($request)
                ->addAddress($request)
                ->addPracticeArea($request)
                ->addStates($request)
                ->addCounties($request)
                ->addLanguage($request);
            DB::commit();
            return successResponse('Profile completed successfully');
        } catch (\Throwable $e) {
            DB::rollBack();
            return failedResponse('Failed submitting profile');
        }

    }

    /**
     * @param Request $request
     * @return $this
     */
    private function createProfile(Request $request): self
    {
        $request->user()->profile()->create(
            $request->only([
                'business_name',
                'website',
                'bio',
            ])
        );
        $this->addProfilePoints();
        return $this;
    }

    /**
     * @param Request $request
     * @return $this
     */
    private function addAddress(Request $request): self
    {
        $request->user()->address()->create(
            $request->only([
                'address_1',
                'address_2',
                'city',
                'state_id',
                'zip_code',
                'contact'
            ])
        );
        $this->addProfilePoints();
        return $this;
    }

}
