<?php

namespace App\Http\Controllers\Api\V1\Attorney\ResourceCenter\Post;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\PostHasAttachment;
use App\Traits\FileUpload\FileUpload;
use App\Traits\Pagination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;

class PostController extends Controller
{
    use FileUpload;
    use Pagination;
    public function index(Request $request): JsonResponse
    {
        $take = $request->page_size ?: 10;
        $data =Auth::user()->post()->with('attachments')
            ->withCount('visits','likes')
            ->orderBy('id', 'desc')->paginate($take);
        $paginatedData = $this->paginatedData($data, $request);
        return successPaginationResponse($paginatedData);

    }

    public function discover(Request $request): JsonResponse
    {
        $take = $request->page_size ?: 10;
        $data = Post::with('attachments')
            ->withCount('visits','likes')
            ->orderBy('id', 'desc')->paginate($take);
        $paginatedData = $this->paginatedData($data, $request);
        return successPaginationResponse($paginatedData);

    }

    public function store(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            if (isset($request->title) && isset($request->description)) {
                $data['content'] = $request->description;
                $data['title'] = $request->title;
                $data['anonymous_user_id'] = $request->post_anonymous ? Auth::user()->id : null;
                $storePost = Auth::user()->post()->create($data);
                $this->handlePostDocument($request, $storePost);
                DB::commit();
            } else {
                return failedResponse(trans('response.titleAndContentRequired'));
            }
            return successDataResponse($storePost->load('attachments'));

        } catch (\Exception $exception) {
            DB::rollBack();
            return failedResponse($exception->getMessage());
        }
    }

    public function show($id): JsonResponse
    {
        $data = Post::with( 'attachments','replies')
            ->withCount('visits','likes')->findOrFail($id);
        return successDataResponse($data);

    }

    public function destroy(int $id): JsonResponse
    {
        if (Auth::user()->post()->where('id', $id)->delete()) {
            return successResponse(trans('response.postDeleted'));
        }
        return failedResponse(trans('response.failedDeletingPost'));
    }

    public function search(Request $request): JsonResponse
    {
        $searchTerm = $request->input('search');
        $take = $request->page_size ?: 10;
        $posts = Post::where('title', 'LIKE', "%{$searchTerm}%")
            ->orWhere('content', 'LIKE', "%{$searchTerm}%")
            ->with('attachments')->withCount('visits','likes')
            ->orderBy('id', 'desc')->paginate($take);
        $paginatedData = $this->paginatedData($posts, $request);

       if($posts){
           return successPaginationResponse($paginatedData);
       }
        return failedResponse(trans('response.noResultFound'));
    }


    private function handlePostDocument(Request $request, Post $postData): void
    {
        if ($documents = $request->file('post_document')) {

            foreach ($documents as $document) {
                $fileName = $this->uploadToS3($document, PostHasAttachment::POST_DOCUMENT_DIR);
                if (!$fileName) continue;
                $attachmentID = $this->uploadAttachment($document, $fileName, PostHasAttachment::POST_DOCUMENT_DIR);
                $postData->attachments()->attach([
                    $attachmentID
                ]);
            }
        }
    }

}
