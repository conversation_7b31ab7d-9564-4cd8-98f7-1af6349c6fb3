<?php

namespace App\Http\Controllers\Api\V1\Attorney\ResourceCenter\PostVisit;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\PostHasAttachment;
use App\Models\PostVisit;
use App\Traits\FileUpload\FileUpload;
use App\Traits\Pagination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use PHPUnit\Exception;

class PostVisitController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $postVisit = PostVisit::updateOrCreate(['user_id' => Auth::user()->id],
                [
                    'post_id' => $request->post_id,
                    'user_id' => Auth::user()->id,
                    'visitor_ip' => $request->ip(),

                ]);
            DB::commit();
            return successDataResponse($postVisit);

        } catch (Exception $exception) {
            DB::rollBack();
            return failedResponse($exception->getMessage());
        }

    }

    public function visitcount($postId)
    {
        $visitCount = PostVisit::where('post_id', $postId)->count();
        return successDataResponse($visitCount);
    }

}
