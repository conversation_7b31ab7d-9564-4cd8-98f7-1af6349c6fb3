<?php

namespace App\Http\Controllers\Api\V1\Attorney\ResourceCenter\PostLike;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\PostHasAttachment;
use App\Models\PostLike;
use App\Models\PostVisit;
use App\Traits\FileUpload\FileUpload;
use App\Traits\Pagination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use PHPUnit\Exception;

class PostLikeController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $like = PostLike::where('user_id', auth()->id())
                ->where('post_id', $request->post_id)->first();
            if ($like) {
               $like = $like->delete();
            } else {
                $like = PostLike::create([
                    'user_id' => auth()->id(),
                    'post_id' => $request->post_id,
                ]);
            }
            DB::commit();
            return successDataResponse($like);

        } catch (Exception $exception) {
            DB::rollBack();
            return failedResponse($exception->getMessage());
        }

    }

    public function visitcount($postId)
    {
        $visitCount = PostVisit::where('post_id', $postId)->count();
        return successDataResponse($visitCount);
    }

}
