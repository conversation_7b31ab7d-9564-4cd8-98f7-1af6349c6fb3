<?php

namespace App\Http\Controllers\Api\V1\Attorney\ResourceCenter\PostReply;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\PostHasAttachment;
use App\Models\PostReplies;
use App\Models\PostVisit;
use App\Traits\FileUpload\FileUpload;
use App\Traits\Pagination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use PHPUnit\Exception;

class PostReplyController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            if (isset($request->description)) {
                $data['content'] = $request->description;
                $data['post_id'] = $request->post_id;
                $data['user_id'] = Auth::user()->id;
                $data['parent_reply_id'] = $request->parent_reply_id ?? null;
                $storeReply = PostReplies::create($data);
                DB::commit();
            } else {
                return failedResponse(trans('response.ContentRequired'));
            }
            return successDataResponse($storeReply);

        } catch (\Exception $exception) {
            DB::rollBack();
            return failedResponse($exception->getMessage());
        }

    }

    public function visitcount($postId)
    {
        $visitCount = PostVisit::where('post_id', $postId)->count();
        return successDataResponse($visitCount);
    }

}
