<?php

namespace App\Http\Controllers\Api\V1\Attorney\Message;

use App\Events\ChatEvent;
use App\Http\Controllers\Controller;
use App\Models\Message;
use App\Traits\Pagination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;


class MessageController extends Controller
{


    use Pagination;

    public function index()
    {
        $messages = Auth::user()->messages()->user()->get();
        return successDataResponse($messages);
    }

    public function fetchMessages()
    {

        $userId = auth()->user()->id;
        $messages = Message::with('user')->where(function ($query) use ($userId) {
            $query->where('from_user_id', $userId)
                ->orWhere('to_user_id', $userId);

        })->groupBy('from_user_id', 'to_user_id')
            ->orderBy('id','desc')->get();

        return successDataResponse($messages);

    }

    public function getMessagesFor(Request $request, $id)
    {
        $take = $request->page_size ?: 10;
        Message::with('user')->where('from_user_id', $id)->where('to_user_id', auth()->id())->update(['read_status' => true]);
        $messages = Message::with('user')->where(function ($q) use ($id) {
            $q->where('from_user_id', auth()->id());
            $q->where('to_user_id', $id);
        })->orWhere(function ($q) use ($id) {
            $q->where('from_user_id', $id);
            $q->where('to_user_id', auth()->id());
        })->orderBy('id','desc')->paginate($take);
        $paginatedData = $this->paginatedData($messages, $request);
        return successPaginationResponse($paginatedData);

    }

    public function sendMessage(Request $request)
    {
        $message = auth()->user()->messages()->create([
            'message' => $request->message,
            'to_user_id' => $request->to_user_id,
        ]);
        broadcast(new ChatEvent($message->message, $request->to_user_id));
         return successDataResponse($message);
    }

    public function destroy($id)
    {
        Message::where('from_user_id', $id)
                ->where('to_user_id', auth()->id())
                ->Orwhere('to_user_id', $id)->where('from_user_id',auth()->id())
                ->delete();
        return successDataResponse(trans('response.messageDeleted'));

    }
}
