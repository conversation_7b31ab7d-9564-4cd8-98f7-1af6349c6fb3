<?php

namespace App\Http\Controllers\Api\V1\Attorney;

use App\Http\Requests\PracticeArea\PracticeAreaUpdateRequest;
use App\Models\PracticeArea;
use App\Traits\UserDetailsUpdate\UserInfoUpdate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PracticeAreaController
{
    use UserInfoUpdate;
    /**
     * @return JsonResponse
     */
    public function index() : JsonResponse
    {
        $data =  PracticeArea::orderBy('name')->get(['id','name']);
        return successDataResponse($data);
    }

    public function update(PracticeAreaUpdateRequest $request) : JsonResponse
    {
        $this->addPracticeArea($request);
        return successResponse(trans('response.infoUpdated'));
    }
}
