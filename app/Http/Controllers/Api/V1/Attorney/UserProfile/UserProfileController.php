<?php

namespace App\Http\Controllers\Api\V1\Attorney\UserProfile;

use App\Http\Requests\Registration\ProfilleCompletionRequest;
use App\Models\Proposal;
use App\Models\User;
use App\Traits\FileUpload\FileUpload;
use App\Traits\ProfilePoints\ProfilePoints;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;

class UserProfileController extends Controller
{

    use ProfilePoints;
    use FileUpload;
    protected $hidden = ['created_at', 'updated_at'];

    /**
     * @param ProfilleCompletionRequest $
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = Auth::user();
        $data['address'] = $data->address;
        $data['profile'] = $data->profile;
        $data['userLanguage'] = $data->userLanguage;
        $data['practiceArea'] = $data->practiceArea;
        $data['attorneyExperience'] =  $data->attorneyExperience;
        $data['states'] =  $data->states;
        $data['state_with_county'] = $data->attorneyState()->with('county:id,name,state_id')->get();
        $data['rating_count'] = $data->rating->avg('rating');
        $data['completed_contract'] = $data->attorneyProposal->where('status', Proposal::COMPLETED)->count();
        return successDataResponse($data);
    }

    /**
     * @return string[]
     */


    private function getRelation(): array
    {
        return ['address', 'profile', 'userLanguage', 'practiceArea', 'states'];
    }

    public function updateBio(Request $request)
    {

        Auth::user()->profile()->update(['bio' => $request->bio]);
        return successResponse('Info updated successfully.');
    }

    public function updateConsultationAmount(Request $request)
    {

        Auth::user()->update(['consultation_fee' => $request->consultation_fee]);
        return $this->index();
    }

    public function updateAvatar(Request $request)
    {
        $avatar = $this->handleAvatar($request);
        $data = Auth::user()->update(['avatar' => $avatar]);
        $this->addProfilePoints();
        return successDataResponse(['profile_points'=> session('profile_points') ?? Auth::user()->profile_points,
            'avatar'=>Auth::user()->avatar]);
    }

    private function handleAvatar(Request $request)
    {
        if ($avatar = $request->file('avatar')) {
                $fileName = $this->uploadToS3($avatar, User::AVATAR_DIR);
                if ($fileName) {
                    $attachmentID = $this->uploadAttachment($avatar, $fileName, User::AVATAR_DIR);
                }
            return $fileName;


        }
    }

    public function getById($id)
    {
        $user = User::with( 'practiceArea:id,name',
            'attorneyExperience',
            'profile',
            'userLanguage',
            'consultationWindow')->findOrFail($id);
        return successDataResponse($user);
    }


}
