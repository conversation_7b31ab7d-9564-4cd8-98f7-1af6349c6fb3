<?php

namespace App\Http\Controllers\Api\V1\Attorney\WorkExperience;

use App\Http\Controllers\Controller;
use App\Http\Requests\WorkExperienceRequest;
use App\Http\Resources\Attorney\WorkExperienceResource;
use App\Models\WorkExperience;
use App\Traits\ProfilePoints\ProfilePoints;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class WorkExperienceController extends Controller
{

    use ProfilePoints;
    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = Auth::user()->attorneyExperience()->get();
        return successDataResponse($data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse
     */
    public function store(WorkExperienceRequest $request): JsonResponse
    {
        try {
            $data = $request->all();
            if(Auth::user()->attorneyExperience()->count() <=0){
                $this->addProfilePoints();
            }
            Auth::user()->attorneyExperience()->create($data);
            $workExperience = Auth::user()->attorneyExperience()->get();
            $workExperience->profile_points = session('profile_points') ?? Auth::user()->profile_points;
            return successDataResponse($workExperience);
        } catch (\Exception $exception) {
            return failedResponse($exception->getMessage());
        }
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function edit($id): JsonResponse
    {
        $status = Auth::user()->attorneyExperience()->findOrFail($id);
        return successResponse(trans('response.workExperienceUpdated'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param WorkExperienceRequest $request
     * @return JsonResponse
     */
    public function update(WorkExperienceRequest $request): JsonResponse
    {
        try {
            $workExperience = Auth::user()->attorneyExperience()->findOrFail($request->id);
            $workExperience->fill($request->all())->save();
            return successResponse(trans('response.workExperienceUpdated'));
        } catch (\Exception $exception) {
            return failedResponse(trans('response.failedUpdatingWorkExperience'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        try {
           Auth::user()->attorneyExperience()->findOrFail($id)->delete();
           return successResponse(trans('response.workExperienceRemoved'));
        } catch (\Exception $exception) {
            return failedResponse(trans('response.failedRemovingWorkExperience'));
        }
    }
}
