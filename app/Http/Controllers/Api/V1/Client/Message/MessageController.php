<?php

namespace App\Http\Controllers\Api\V1\Client\Message;

use App\Events\ChatEvent;
use App\Events\MessageDelivered;
use App\Events\MessageSeen;
use App\Http\Controllers\Controller;
use App\Models\Message;
use App\Models\Proposal;
use App\Models\User;
use App\Traits\Pagination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Session;

class MessageController extends Controller
{

    use Pagination;

    public function index(): JsonResponse
    {
        $messages = Auth::user()->messages()->user()->get();
        return successDataResponse($messages);
    }

    public function fetchMessages(Request $request): JsonResponse
    {
        $take = $request->page_size ?: 10;
        $userId = Auth::user()->id;
        $users = Message::with('user')
            ->select(DB::raw('CASE WHEN messages.from_user_id = '.$userId.' THEN messages.to_user_id
                                         WHEN messages.to_user_id = '.$userId.' THEN messages.from_user_id
                                         END as user_id, messages.created_at, messages.id, message,read_status,delivered,from_user_id,to_user_id,type,inquiry_id,proposal_id'))
            ->whereIn('messages.id', function ($query) use ($userId) {
                $query->select(DB::raw('MAX(messages.id)'))
                    ->from('messages')
                    ->where('from_user_id', $userId)
                    ->orWhere('to_user_id', $userId)
                    ->groupBy(DB::raw('CASE WHEN messages.from_user_id = '.$userId.' THEN messages.to_user_id
                                                  WHEN messages.to_user_id = '.$userId.' THEN messages.from_user_id END'));
            })
            ->orderBy('messages.created_at', 'desc')
            ->paginate($take);

        $paginatedData = $this->paginatedData($users, $request);
        return successPaginationResponse($paginatedData);
    }

//    public function fetchMessages()
//    {
//        $userId = auth()->user()->id;
//        $messages = Message::with('user')->where(function ($query) use ($userId) {
//            $query->where('from_user_id', $userId)
//                ->orWhere('to_user_id', $userId);
//
//        })->groupBy('from_user_id', 'to_user_id')
//            ->orderBy('id','desc')->get();
//
//        return successDataResponse($messages);
//
//    }




    public function messageObject($message)
    {
        $talkedUser = Auth::user()->talkedTo()->pluck('to_user_id', 'message')->toArray();
        $relatedUser = Auth::user()->relatedTo()->pluck('from_user_id', 'message')->toArray();
        $userIds = array_unique(array_merge($talkedUser, $relatedUser));
        $users = User::with('talkedTo', 'relatedTo')
            ->whereIn('id', array_values($userIds))
            ->where('id', ' != ', \auth()->user()->id)
            ->latest()->first();
        return $users;

    }

    public function getMessagesFor(Request $request, $id): JsonResponse
    {
        $take = $request->page_size ?: 10;
        Message::where('from_user_id', $id)->where('to_user_id', auth()->id())->update(['read_status' => true]);
        $messages = Message::with('sender')
//            ->where('inquiry_id', $inquiryId)
//            ->where('proposal_id', $proposalId)
//            ->where('type','!=', 10)
            ->where(function ($q) use ($id) {
                $q->where('from_user_id', auth()->id());
                $q->where('to_user_id', $id);
            })->orWhere(function ($q) use ($id) {
                $q->where('from_user_id', $id);
                $q->where('to_user_id', auth()->id());
            })->orderBy('id', 'desc')->paginate($take);
        $paginatedData = $this->paginatedData($messages, $request);
        return successPaginationResponse($paginatedData);
    }

    public function sendMessage(Request $request): JsonResponse
    {
        $message = Message::where(function ($query) use ($request) {
            $query->where('to_user_id', $request->to_user_id)
                ->where('from_user_id', auth()->id())
                ->where('proposal_id', $request->proposal_id)
                ->where('inquiry_id', $request->inquiry_id);
        })
            ->orWhere(function ($query) use ($request) {
                $query->where('from_user_id', $request->to_user_id)
                    ->where('to_user_id', auth()->id())
                    ->where('proposal_id', $request->proposal_id)
                    ->where('inquiry_id', $request->inquiry_id);
            })
            ->orderBy('created_at', 'desc')
            ->first();

        $message = auth()->user()->messages()->create([
            'message' => $request->message,
            'to_user_id' => $request->to_user_id,
//            'proposal_id' => $request->proposal_id ?? $message->proposal_id ?? null,
//            'inquiry_id' => $request->inquiry_id ?? $message->inquiry_id ?? null,
//            'type' => $request->type ?? null,
        ]);
        $user = User::findOrFail($request->to_user_id);
        broadcast(new ChatEvent($message, $user));
        $this->sendNotification($message,$request);
        return successDataResponse($message->with('sender')->latest()->first());
    }

    public function destroy($id): JsonResponse
    {

        $deletedBy[] = Auth::id();
        Message::where('from_user_id', $id)
            ->where('to_user_id', auth()->id())
            ->Orwhere('to_user_id', $id)->where('from_user_id', auth()->id())
            ->update(['deleted_by' => $deletedBy]);

        Message::where('from_user_id', $id)
            ->where('to_user_id', auth()->id())
            ->Orwhere('to_user_id', $id)->where('from_user_id', auth()->id())
            ->delete();

        return successResponse(trans('response.messageDeleted'));

    }

    public function archive($id): JsonResponse
    {

        $archiveBy[] = Auth::id();
        Message::where('from_user_id', $id)
            ->where('to_user_id', auth()->id())
            ->Orwhere('to_user_id', $id)->where('from_user_id', auth()->id())
            ->update(['archive_by' => $archiveBy]);

        Message::where('from_user_id', $id)
            ->where('to_user_id', auth()->id())
            ->Orwhere('to_user_id', $id)->where('from_user_id', auth()->id())
            ->delete();

        return successResponse(trans('response.messageArchived'));

    }

    public function messageCount(): JsonResponse
    {
        $count = Message::where('to_user_id', Auth::user()->id)->where('read_status', 0)->count();
        return successDataResponse($count);

    }

    public function seen($userId): JsonResponse
    {
        $message = Message::where('from_user_id', $userId)
            ->where('to_user_id', Auth::user()->id)
            ->latest()->first();

        if ($message) {
            $message->read_status = 1;
            $message->save();
        }
        $user = User::findOrFail($userId);
        broadcast(new MessageSeen($message, $user));
        return successResponse(trans('response.messageSeen'));

    }

    public function delivered($userId): JsonResponse
    {

        $message = Message::where('from_user_id',$userId)
            ->where('to_user_id', Auth::user()->id)
            ->latest()->first();

        if ($message) {
            $message->delivered = 1;
            $message->save();
        }

        $user = User::findOrFail($userId);
        broadcast(new MessageDelivered($message, $user));
        return successResponse(trans('response.messageDelivered'));
    }

    /**
     * @throws \Exception
     */
    public function sendNotification($message, $request)
    {
        $cacheKey = Cache::get('chat_session_'.Auth::user()->id.'-'.$request->to_user_id);
        //if ($cacheKey == null) {
           sendNotification('', $message->message, fcmToken($request->to_user_id), $this->messageObject($message), 1,$request->to_user_id);
        //}
    }

    public function notificationCacheCheck(Request $request): JsonResponse
    {
        if ($request->active == 1) {
            Cache::put('chat_session_'.$request->to_user_id.'-'.Auth::user()->id, $request->to_user_id);
            return successDataResponse(Cache::get('chat_session_'.$request->to_user_id.'-'.Auth::user()->id),'User is in the chat box');
        } else {
            Cache::forget('chat_session_'.$request->to_user_id.'-'.Auth::user()->id);
            return successDataResponse(Cache::get('chat_session_'.$request->to_user_id.'-'.Auth::user()->id),'User is not in the chat box');
        }
    }
}
