<?php

namespace App\Http\Controllers\Api\V1\Client\Contract;

use App\Http\Controllers\Controller;
use App\Models\Proposal;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ContractController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request): JsonResponse
    {
        $status = $request->status ?: Proposal::PENDING;
        $take = $request->page_size ?: 10;
        $data = $this->getProposedInquiry()
            ->whereHas('attorneyProposal', function ($q) use ($status) {
                $q->where('status', $status);
            })->paginate($take);
        $paginatedData = $this->paginatedData($data, $request);
        return successPaginationResponse($paginatedData);
    }

}
