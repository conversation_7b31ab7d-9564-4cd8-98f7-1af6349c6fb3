<?php

namespace App\Http\Controllers\Api\V1\Client\News;

use App\Http\Controllers\Controller;
use App\Traits\Pagination;
use \Illuminate\Http\JsonResponse;
use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    use Pagination;

    public function index(Request $request): JsonResponse
    {
        $take = $request->page_size ?: 10;
        $news = News::paginate($take);
        $paginatedData = $this->paginatedData($news, $request);
        return successPaginationResponse($paginatedData);
    }

    public function show($id): JsonResponse
    {
        try {
            $news = News::find($id);
            return successDataResponse($news);
        } catch (\Exception $e) {
            return failedResponse(trans('response.newsNotFound'));
        }
    }


}
