<?php

namespace App\Http\Controllers\Api\V1\Client\Inquiry;

use App\Http\Controllers\Controller;
use App\Http\Resources\Client\InquiryResource;
use App\Models\County;
use App\Models\Inquiry;
use App\Models\InquiryHasAttachment;
use App\Models\InquiryHasPracticeArea;
use App\Models\State;
use App\Traits\FileUpload\FileUpload;
use App\Traits\Pagination;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class InquiryController extends Controller
{
    use FileUpload;
    use Pagination;

    public function index(Request $request): JsonResponse
    {
        $userId = Auth::user()->id;
        $take = $request->page_size ?: 10;
        $data = Inquiry::where('user_id', $userId)->with('practice_areas', 'attachments')
            ->orderBy('id', 'desc')->paginate($take);
        $paginatedData = $this->paginatedData($data, $request);
        return successPaginationResponse($paginatedData);

    }

    public function store(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            if (isset($request->latitude) && isset($request->longitude)) {
                $locationData = $this->getCountiesGeoCode($request->latitude,$request->longitude);
                //$locationData = DB::select(DB::raw("SELECT * from counties as c where ST_CONTAINS(c.geometry, ST_GeomFromText(ST_AsText(point($request->longitude,$request->latitude))))"));
                $data = $request->except('practice_area_id', 'inquiry_document');
                if (isset($locationData) && $locationData != null) {
                    $data['state_id'] = $locationData->state_id;
                    $data['county_id'] = $locationData->id;
                    $storeInquiry = Auth::user()->inquiry()->create($data);
                    $storeInquiry->practice_areas()->attach($request->practice_area_id);
                    $this->handleInquiryDocument($request, $storeInquiry);
                    DB::commit();
                }else{
                    return failedResponse(trans('response.invalidTargetLocation'));
                }
                return successDataResponse($storeInquiry->load('practice_areas', 'attachments'));
            }

        } catch (\Exception $exception) {
            DB::rollBack();
            return failedResponse($exception->getMessage());
        }

    }

    public function show($id): JsonResponse
    {
        $data = Inquiry::with('practice_areas', 'attachments')->findOrFail($id);
        return successDataResponse($data);

    }

    public function destroy(int $id): JsonResponse
    {
        if (Auth::user()->inquiry()->where('id', $id)->delete()) {
            return successResponse(trans('response.questionDeleted'));
        }
        return failedResponse(trans('response.failedDeletingQuestion'));
    }

    private function handleInquiryDocument(Request $request, Inquiry $inquiryData): void
    {
        if ($documents = $request->file('inquiry_document')) {
            foreach ($documents as $document) {
                $fileName = $this->uploadToS3($document, InquiryHasAttachment::INQUIRY_DOCUMENT_DIR);
                if (!$fileName) continue;
                $attachmentID = $this->uploadAttachment($document, $fileName, InquiryHasAttachment::INQUIRY_DOCUMENT_DIR);
                $inquiryData->attachments()->attach([
                    $attachmentID
                ]);
            }
        }
    }

    public function getCountiesGeoCode($lat,$lng)
    {
        $url = "https://maps.googleapis.com/maps/api/geocode/json?latlng={$lat},{$lng}&key=AIzaSyBT0o4a7Alu8op3VkfbslnldHaJKF1TjOI";
        $response = file_get_contents($url);
        $response = json_decode($response, true);
        $county = null;
        $state = null;
        foreach ($response['results'][0]['address_components'] as $addressComponent) {
            if (in_array('administrative_area_level_1', $addressComponent['types'])) {
                $state = $addressComponent['short_name'];
                continue;
            }
            if (in_array('administrative_area_level_2', $addressComponent['types'])) {
                $county = $addressComponent['long_name'];
            }
        }

        $specialCases = [
            'DC' => 'Washington',
            'VA' => 'Independent City',
            'MD' => 'Baltimore',
            'MO' => 'St. Louis',
            'AK' => 'Borough or Census Area',
            'HI' => 'County Equivalent',
            'LA' => 'Parish'
        ];

        if (array_key_exists($state, $specialCases)) {
            if ($state === 'DC') {
                $county = 'Washington';
            } elseif ($state === 'VA' && empty($county)) {
                // For Virginia, if no county is found, it may be an independent city
                foreach ($response['results'] as $result) {
                    foreach ($result['address_components'] as $component) {
                        if (in_array('locality', $component['types'])) {
                            $county = $component['long_name'];
                            break 2;
                        }
                    }
                }
            } elseif (in_array($state, ['MD', 'MO']) && empty($county)) {
                // For Baltimore in Maryland and St. Louis in Missouri
                foreach ($response['results'] as $result) {
                    foreach ($result['address_components'] as $component) {
                        if (in_array('locality', $component['types']) && $component['long_name'] === $specialCases[$state]) {
                            $county = $component['long_name'];
                            break 2;
                        }
                    }
                }
            }
            // Additional special handling for Alaska, Hawaii, Louisiana can be added here as needed
        }

        if ($county) {
            $state = State::where('abbr',$state)->first();
            if($state) {
                $addorUpdateCounty = County::updateOrCreate(['name' => trim(str_replace('County', "", $county))], ['name' => trim(str_replace('County', "", $county)), 'state_id' => $state->id]);
                return $addorUpdateCounty;
            }
        }
        return null;
    }

    public function update(Request $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $inquiry = Auth::user()->inquiry()->findOrFail($id);

            if (isset($request->latitude) && isset($request->longitude)) {
                $locationData = $this->getCountiesGeoCode($request->latitude, $request->longitude);
                $data = $request->except('practice_area_id', 'inquiry_document');

                if (isset($locationData) && $locationData != null) {
                    $data['state_id'] = $locationData->state_id;
                    $data['county_id'] = $locationData->id;
                    $inquiry->update($data);

                    if ($request->has('practice_area_id')) {
                        $inquiry->practice_areas()->sync($request->practice_area_id);
                    }

                    if ($request->has('attachment_delete_ids') && !empty($request->attachment_delete_ids)) {
                        InquiryHasAttachment::whereIn('attachment_id', $request->attachment_delete_ids)->delete();
                    }

                    $this->handleInquiryDocument($request, $inquiry);
                    DB::commit();
                } else {
                    return failedResponse(trans('response.invalidTargetLocation'));
                }
                return successDataResponse($inquiry->load('practice_areas', 'attachments'));
            }

        } catch (\Exception $exception) {
            DB::rollBack();
            return failedResponse($exception->getMessage());
        }
    }

    public function deleteInquiryAttachments(Request $request): JsonResponse
    {
        try {
            $inquiry_id = Auth::user()->inquiry()->findOrFail($request->inquiry_id)->id;
            InquiryHasAttachment::where('attachment_id', $request->attachment_id)->where('inquiry_id', $inquiry_id)->delete();
            return successResponse(trans('response.attachmentDeleted'));
        }catch (\Exception $exception){
            return failedResponse($exception->getMessage());
        }
    }

}
