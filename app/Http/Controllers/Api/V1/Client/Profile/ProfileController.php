<?php

namespace App\Http\Controllers\Api\V1\Client\Profile;

use App\Http\Controllers\Controller;
use App\Http\Resources\Client\ProfileResource;
use App\Models\User;
use App\Traits\FileUpload\FileUpload;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProfileController extends Controller
{
    use FileUpload;

    public function index( ): JsonResponse
    {
        $data = Auth::user()->load(['address','userLanguage']);
        return successDataResponse($data);
    }

    public function updateAvatar(Request $request): JsonResponse
    {
        $avatar = $this->handleAvatar($request);
        $data = Auth::user()->update(['avatar' => $avatar]);
        $this->addProfilePoints();
        return successDataResponse(['profile_points'=> session('profile_points') ?? Auth::user()->profile_points,
            'avatar'=>Auth::user()->avatar]);
    }

    private function handleAvatar(Request $request)
    {
        if ($avatar = $request->file('avatar')) {
            $fileName = $this->uploadToS3($avatar, User::AVATAR_DIR);
            if ($fileName) {
                $attachmentID = $this->uploadAttachment($avatar, $fileName, User::AVATAR_DIR);
            }
            return $fileName;


        }
    }

    public function getById($id): JsonResponse
    {
        $user = User::findOrFail($id);
        return successDataResponse($user);
    }

}





