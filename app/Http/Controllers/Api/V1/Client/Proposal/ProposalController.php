<?php

namespace App\Http\Controllers\Api\V1\Client\Proposal;

use App\Http\Controllers\Controller;
use App\Models\Inquiry;
use App\Models\Proposal;
use App\Models\ProposalDocument;
use App\Traits\Attorney\UserRelatedID;
use App\Traits\FileUpload\FileUpload;
use App\Traits\Pagination;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProposalController extends Controller
{
    use FileUpload;
    use UserRelatedID;
    use Pagination;
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request): JsonResponse
    {
        $status = $request->status ?: Proposal::PENDING;
        $take = $request->page_size ?: 10;
        $data = $this->getProposals($status)
           ->paginate($take);
        $paginatedData = $this->paginatedData($data, $request);
        return successPaginationResponse($paginatedData);
    }

    public function get(int $id): JsonResponse
    {
        $data = Proposal::with('attachments')->where('id', $id)->first();
        return successDataResponse($data);

    }

    private function getProposals($status): Builder
    {
        return Inquiry::query()
            ->with('client:id,name,email,avatar')
            ->with('attachments')
            ->with(strtolower($status).'proposal:id,status,user_id,inquiry_id,description,created_at')
            ->with(strtolower($status).'proposal.attorneyuser:id,name,email,avatar')
            ->with(strtolower($status).'proposal.attorneyuser.practiceArea:id,name')
            ->with(strtolower($status).'proposal.attachments')
           ->where('user_id',Auth::user()->id)
            ->whereHas('proposal', function ($query) use ($status) {
                $query->where('status',$status);
            })
            ->select(['id', 'title', 'message', 'user_id', 'created_at']);
    }


    public function accept($proposal_id): JsonResponse
    {
        $accept = Proposal::where('id',$proposal_id)->update(['status'=>'active']);
        $proposal = Proposal::findOrFail($proposal_id);
        sendNotification("LegalFiber",trans('response.proposalAccepted'),fcmToken($proposal->user_id),null,2);
        return successResponse(trans('response.contractStart'));
    }

    public function complete($proposal_id): JsonResponse
    {
        $complete = Proposal::where('id',$proposal_id)->update(['status'=>'completed']);
        return successResponse(trans('response.contractCompleted'));
    }

}
