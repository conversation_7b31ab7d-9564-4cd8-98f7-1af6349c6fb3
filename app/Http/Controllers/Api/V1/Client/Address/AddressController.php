<?php

namespace App\Http\Controllers\Api\V1\Client\Address;

use App\Http\Controllers\Controller;
use App\Http\Requests\Address\AddressStoreRequest;
use App\Http\Resources\Client\AddressResource;
use App\Models\Address;
use App\Traits\ProfilePoints\ProfilePoints;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AddressController extends Controller
{
    use ProfilePoints;
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(): JsonResponse
    {
        $data = Auth::user()->address()->get();
        return successDataResponse($data);
    }

    /**
     * @param AddressStoreRequest $request
     * @return JsonResponse
     */
    public function store(AddressStoreRequest $request): JsonResponse
    {
        try {
            $addressData = Auth::user()->address()->create($request->all());
            $this->addProfilePoints();
            $addressData['profile_points'] = session('profile_points') ?? Auth::user()->profile_points;
            return successDataResponse(AddressResource::make($addressData));
        } catch (\Throwable $e) {
            return failedResponse(trans('response.failedAddingAddress'));
        }
    }

    /**
     * @param AddressStoreRequest $request
     * @return JsonResponse
     */
    public function update(AddressStoreRequest $request): JsonResponse
    {
        try {
            $address = Address::findOrFail($request->id);
            $address->fill($request->all())->save();
            return successDataResponse(AddressResource::make($address));
        } catch (\Exception $e) {
            return failedResponse(trans('response.failedUpdatingAddress'));
        }
    }

    /**
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            Auth::user()->address()->findOrFail($id)->delete();
            return successResponse(trans('response.addressRemoved'));
        } catch (\Exception $exception) {
            return failedResponse(trans('response.failedRemovingAddress'));
        }
    }
}
