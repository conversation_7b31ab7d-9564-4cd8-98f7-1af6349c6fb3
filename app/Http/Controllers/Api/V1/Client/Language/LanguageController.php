<?php

namespace App\Http\Controllers\Api\V1\Client\Language;

use App\Http\Controllers\Controller;
use App\Http\Requests\Language\LanguageStoreRequest;
use App\Http\Resources\Client\LanguageResource;
use App\Models\Language;
use App\Traits\Pagination;
use App\Traits\ProfilePoints\ProfilePoints;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class LanguageController extends Controller
{
    use Pagination;
    use ProfilePoints;

    public function index(): JsonResponse
    {
        $languages = Language::all();
        return successDataResponse(LanguageResource::make($languages));
    }

    public function store(LanguageStoreRequest $request): JsonResponse
    {
        if($request->user()->userLanguage()->count() <= 0) {
            $this->addProfilePoints();
        }
        $request->user()->userLanguage()->sync($request->language_id);
        return successDataResponse(['profile_points'=> session('profile_points') ?? Auth::user()->profile_points],trans('response.languageAdded'));

    }


}
