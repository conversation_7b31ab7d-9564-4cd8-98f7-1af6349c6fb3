<?php

namespace App\Http\Controllers\Api\V1\Client\AudioVideo;

use App\Http\Controllers\Controller;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Kutia\Larafirebase\Facades\Larafirebase;


class AudioVideoController extends Controller
{

    protected $projectId;
    protected $serverKey;

    public function __construct()
    {
        $this->projectId = 'legal-fiber';
        $this->serverKey = env('FIREBASE_SERVER_KEY');
    }
    public function create(Request $request): \Illuminate\Http\JsonResponse
    {
        Log::debug($request->all());
        $call = auth()->user()->messages()->create([
            'message' => "Call Log",
            'to_user_id' => $request->to_user_id,
            'type' => $request->video ? 2 : 1
        ]);
        sendNotification("LegalFiber",
            Auth::user()->name . " is calling..",
            fcmToken($request->to_user_id),
            null,
            8,
            $request->to_user_id,
            null,
            $request->callId,
            $request->video,
            $request->deviceId,
            $request->apnCallId,
            $request->callLog,
        );
        return successDataResponse($call);
    }

    public function getRtcToken(Request $request): \Illuminate\Http\JsonResponse
    {
        $token = rtcToken($request->uid, $request->channelName);
        return successDataResponse($token);
    }

    public function callEnd(Request $request)
    {
        sendNotification("LegalFiber",
            "Call Ended",
            fcmToken($request->to_user_id),
            null,
            9,
            $request->to_user_id,

        );
    }

    public function handleCallAction(Request $request): \Illuminate\Http\JsonResponse
    {
        $documentId = $request->data['id'];
        $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/legal-fiber/{$documentId}";

        $client = new Client();
        $response = $client->delete($url, [
            'headers' => [
                'Authorization:key=' . env('FIREBASE_SERVER_KEY'),
                'Content-Type: application/json',
            ],
        ]);

        if ($response->getStatusCode() == 200) {
            return successResponse( 'Document deleted successfully', 200);
        } else {
            return failedResponse('Failed to delete document', $response->getStatusCode());
        }
    }


}
