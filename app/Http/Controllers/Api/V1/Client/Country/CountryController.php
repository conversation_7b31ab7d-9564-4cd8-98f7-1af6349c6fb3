<?php

namespace App\Http\Controllers\Api\V1\Client\Country;

use App\Http\Controllers\Controller;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CountryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = Country::orderBy('sorting','desc')->get();
        return successDataResponse($data);
    }


}
