<?php

namespace App\Http\Controllers\Api\V1\Client\Stripe;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Proposal;
use App\Models\StripePayment;
use App\Models\User;
use App\Traits\ProfilePoints\ProfilePoints;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\PaymentMethod;
use Stripe\Stripe;
use Stripe\PaymentIntent;

class StripeController extends Controller
{
    use ProfilePoints;

    /**
     * Creates an intent for payment, so we can capture the payment
     * method for the user.
     *
     * @param Request $request The request data from the user.
     */
    public function getSetupIntent(Request $request)
    {
        return $request->user()->createSetupIntent();
    }

    /**
     * Updates a subscription for the user
     *
     * @param Request $request The request containing subscription update info.
     */
    public function updateSubscription(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = $request->user();
        $planID = $request->get('plan');
        $paymentID = $request->get('payment');

        if (!$user->subscribed('default')) {
            $user->newSubscription('default', $planID)
                ->create($paymentID);
        } else {
            $user->subscription('default')->swap($planID);
        }

        return response()->json([
            'subscription_updated' => true
        ]);
    }

    /**
     * Adds a payment method to the current user.
     *
     * @param Request $request The request data from the user.
     */
    public function postPaymentMethods(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = $request->user();
        $paymentMethodID = $request->get('payment_method');
        if ($user->stripe_id == null) {
            $stripe = $user->createAsStripeCustomer([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);
        }

        $user->addPaymentMethod($paymentMethodID);
        $user->updateDefaultPaymentMethod($paymentMethodID);
        $this->addProfilePoints();

        return response()->json(['profile_points' => session('profile_points') ?? Auth::user()->profile_points], 204);
    }

    /**
     * Returns the payment methods the user has saved
     *
     * @param Request $request The request data from the user.
     */
    public function getPaymentMethods(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = $request->user();

        $methods = array();

        if ($user->hasPaymentMethod()) {
            foreach ($user->paymentMethods() as $method) {
                $methods[] = [
                    'id' => $method->id,
                    'brand' => $method->card->brand,
                    'last_four' => $method->card->last4,
                    'exp_month' => $method->card->exp_month,
                    'exp_year' => $method->card->exp_year,
                    'billing_details' => $method->billing_details,
                ];
            }
        }


        return response()->json($methods);
    }

    /**
     * Removes a payment method for the current user.
     *
     * @param Request $request The request data from the user.
     */
    public function removePaymentMethod(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = $request->user();
        $paymentMethodID = $request->get('id');

        $paymentMethods = $user->paymentMethods();
        foreach ($paymentMethods as $method) {
            if ($method->id == $paymentMethodID) {
                $method->delete();
                $user->pm_type = null;
                $user->pm_last_four = null;
                $user->save();
                break;
            }
        }

        return successResponse(trans('response.removedPaymentMethod'), 200);
    }

    public function chargeClient(Request $request)
    {
        $client = Auth::user();
        $proposalId = $request->input('proposal_id');
        if ($proposalId) {
            $proposal = Proposal::findOrFail($proposalId);
            // Fetch consultation fee
            $consultationFee = $proposal->consultation_fee;
        } else {
            $consultationFee = $request->input('consultation_fee');
        }
        // Fetch client's saved payment method
        Stripe::setApiKey(env('STRIPE_SECRET'));


        $paymentMethodId = $this->getPaymentMethodId($client->stripe_id);
        // Stripe setup

        try {
            // Create a PaymentIntent
            $paymentIntent = PaymentIntent::create([
                'amount' => $consultationFee * 100, // Amount in cents
                'currency' => 'usd',
                'payment_method' => $paymentMethodId,
                'customer' => $client->stripe_id,
                'confirm' => true,
                'return_url' => url('/payment/callback'),
            ]);

            // Save payment details in the database
            $paymentIntent = Payment::create([
                'client_id' => $client->id,
                'attorney_id' => $proposal->user_id,
                'proposal_id' => $proposalId ?? null,
                'stripe_payment_id' => $paymentIntent->id,
                'amount' => $consultationFee,
                'currency' => $paymentIntent->currency,
                'status' => $paymentIntent->status, // e.g., succeeded
            ]);
            return successDataResponse($paymentIntent);
        } catch (\Exception $e) {
            return failedResponse($e->getMessage(), 500);
        }
    }

    public function handleCallback(Request $request)
    {
        // Get PaymentIntent ID and status from the query string
        $paymentIntentId = $request->query('payment_intent');
        $paymentStatus = $request->query('redirect_status');

        if (!$paymentIntentId || !$paymentStatus) {
            return response()->json(['success' => false, 'message' => 'Invalid payment callback data.'], 400);
        }

        try {
            // Fetch PaymentIntent details from Stripe
            Stripe::setApiKey(env('STRIPE_SECRET'));
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            // Update payment status in your database
            $payment = Payment::where('stripe_payment_id', $paymentIntentId)->first();
            if ($payment) {
                $payment->status = $paymentIntent->status; // Update the status
                $payment->save();
            }

            if ($paymentIntent->status === 'succeeded') {
                return response()->json(['success' => true, 'message' => 'Payment successful.']);
            }

            return response()->json(['success' => false, 'message' => 'Payment not completed.'], 400);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function getPaymentMethodId($customerId)
    {
        try {
            // Set the Stripe API key
            Stripe::setApiKey(env('STRIPE_SECRET'));

            // List all payment methods for the customer
            $paymentMethods = PaymentMethod::all([
                'customer' => $customerId,
                'type' => 'card', // Assuming you're looking for card payment methods
            ]);

            // Return the first payment method's ID if available
            if (!empty($paymentMethods->data)) {
                return $paymentMethods->data[0]->id; // First payment method ID
            }

            return response()->json(['message' => 'No payment methods found for this customer.'], 404);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    public function savePaymentInfo(Request $request)
    {
        try {
            StripePayment::create([
                'client_id' => Auth::user()->id,
                'attorney_id' => $request->attorneyId,
                'payment_intent_id' => $request->payment_intent_id,
                'amount' => $request->amount,
                'consultation_window' => true,
            ]);
            return successResponse(trans('response.paymentSaved'), 200);
        } catch (\Exception $e) {
            // Log error, notify user, etc.
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }


}
