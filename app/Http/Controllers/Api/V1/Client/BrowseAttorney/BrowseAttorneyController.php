<?php

namespace App\Http\Controllers\Api\V1\Client\BrowseAttorney;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Traits\Pagination;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BrowseAttorneyController extends Controller
{
    use Pagination;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $attorneys = User::with(
            'practiceArea:id,name',
                    //'attorneyState.county:id,name,state_id',
                    //'attorneyState.states:id,name,abbr',
                    'attorneyExperience',
                    'profile',
                    'userLanguage',
                    'consultationWindow'
                    //'address'
        )->where('user_type', 'ATTORNEY')->paginate($perPage = 10);
        $paginatedData = $this->paginatedData($attorneys,request());

        return successPaginationResponse($paginatedData);
    }

    /**
     * Filter attorneys based on various criteria
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function filter(Request $request): JsonResponse
    {
        $take = $request->page_size ?: 10;

        $query = User::with([
            'practiceArea:id,name',
//            'attorneyState.county:id,name,state_id',
//            'attorneyState.states:id,name,abbr',
            'attorneyExperience',
            'profile',
            'userLanguage',
            'consultationWindow'
        ])->where('user_type', 'ATTORNEY');

        // Filter by practice areas
        if ($request->has('practice_area_ids') && !empty($request->practice_area_ids)) {
            $practiceAreaIds = $request->practice_area_ids;
            $query->whereHas('practiceArea', function($q) use ($practiceAreaIds) {
                $q->whereIn('practice_areas.id', $practiceAreaIds);
            });
        }

        // Filter by states
        if ($request->has('state_ids') && !empty($request->state_ids)) {
            $stateIds = $request->state_ids;
            $query->whereHas('states', function($q) use ($stateIds) {
                $q->whereIn('states.id', $stateIds);
            });
        }

        // Filter by counties
        if ($request->has('county_ids') && !empty($request->county_ids)) {
            $countyIds = $request->county_ids;
            $query->whereHas('attorneyState.county', function($q) use ($countyIds) {
                $q->whereIn('counties.id', $countyIds);
            });
        }

        // Sort by consultation fee
        if ($request->has('sort_direction') && $request->sort_direction) {
            $direction = $request->sort_direction === 'asc' ? 'asc' : 'desc';
            $query->orderBy('consultation_fee', $direction)
                ->select('users.*');
        }

        $attorneys = $query->paginate($take);
        $paginatedData = $this->paginatedData($attorneys, $request);

        return successPaginationResponse($paginatedData);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return JsonResponse
     */
    public function search(Request $request)
    {
        $request->validate([
            'name' => 'required|string'
        ]);

        $attorneys = User::with([
            'practiceArea:id,name',
            'attorneyExperience',
            'profile',
            'userLanguage',
            'consultationWindow'
        ])
            ->where('user_type', 'ATTORNEY')
            ->where('name', 'like', '%' .$request->name. '%')
            ->paginate(10);

        $paginatedData = $this->paginatedData($attorneys, $request);

        return successPaginationResponse($paginatedData);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
