<?php

namespace App\Http\Controllers\Api\V1\Client\Appointment;

use App\Http\Controllers\Controller;
use App\Http\Requests\AppointmentRequest;
use App\Models\Appointment;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AppointmentController extends Controller
{

    public function index()
    {

    }

    public function store(AppointmentRequest $request)
    {
        try {
            if ($this->checkAppointMent($request)) {
                return failedResponse(trans('response.pendingAppointmentExist'));
            }


            $appointment = Auth::user()->appointment()->create($request->all());
            return successDataResponse($appointment, 201);
        } catch (\Exception $exception) {
            return failedResponse($exception->getMessage(), $exception->getCode());
        }
    }

    private function checkAppointMent(Request $request)
    {
        return Auth::user()->appointment()->where('attorney_id', $request->attorney_id)
            ->where('time', '>', Carbon::now())->count();
    }

    public function get(Request $request, $participantID)
    {

        $data = Auth::user()->appointment()
            ->with('attorney:id,name,email,avatar')
            ->where('attorney_id', $participantID)
            ->get();

        return successDataResponse($data);
    }

    public function cancel($appointmentId): JsonResponse
    {
        $appointment = Appointment::findOrFail($appointmentId);
        if(Auth::user()->appointment()
            ->where('id', $appointmentId)
            ->update(['is_approved' => 2])) {
            sendNotification("LegalFiber", trans('response.appointmentCancelled'), fcmToken($appointment->attorney_id), null, 6,$appointment->attorney_id);
            return successResponse(trans('response.appointmentCancelled'));
        }
        return failedResponse(trans('response.failedApprovingAppointment'));
    }

    public function approve($appointmentId): JsonResponse
    {
        $appointment = Appointment::findOrFail($appointmentId);
        if (Auth::user()->appointment()
            ->where('id', $appointmentId)
            ->update(['is_approved' => 1])) {
            sendNotification("LegalFiber", trans('response.appointmentApproved'), fcmToken($appointment->attorney_id), null, 5,$appointment->attorney_id);
            return successResponse(trans('response.appointmentApproved'));
        }
        return failedResponse(trans('response.failedApprovingAppointment'));
    }


}
