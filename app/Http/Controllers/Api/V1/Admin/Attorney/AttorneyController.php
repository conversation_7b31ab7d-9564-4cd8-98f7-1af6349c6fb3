<?php

namespace App\Http\Controllers\Api\V1\Admin\Attorney;

use App\Http\Controllers\Controller;
use App\Models\User as Attorney;
use App\Models\State;
use App\Models\PracticeArea;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class AttorneyController extends Controller
{
    /**
     * Display a listing of attorneys.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Attorney::with(['attorneyState', 'practiceArea'])->where('user_type','ATTORNEY');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by state
        if ($request->filled('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        // Filter by practice area
        if ($request->filled('practice_area_id')) {
            $query->whereHas('practiceArea', function ($q) use ($request) {
                $q->where('practice_areas.id', $request->practice_area_id);
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        
        if ($sortBy === 'name') {
            $query->orderBy('name', $sortDirection);
        } else {
            $query->orderBy($sortBy, $sortDirection);
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $attorneys = $query->paginate($perPage);

        return response()->json($attorneys);
    }

    /**
     * Store a newly created attorney.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:attorneys,email',
            'phone' => 'nullable|string|max:20',
            'license_number' => 'nullable|string|max:50|unique:attorneys,license_number',
            'state_id' => 'required|exists:states,id',
            'status' => 'required|in:active,inactive',
            'bio' => 'nullable|string',
            'years_experience' => 'nullable|integer|min:0',
            'bar_admission_date' => 'nullable|date',
            'practice_area_ids' => 'nullable|array',
            'practice_area_ids.*' => 'exists:practice_areas,id',
        ]);

        $attorney = Attorney::create($validated);

        // Attach practice areas if provided
        if (!empty($validated['practice_area_ids'])) {
            $attorney->practiceArea()->attach($validated['practice_area_ids']);
        }

        $attorney->load(['state', 'practiceAreas']);

        return response()->json([
            'message' => 'Attorney created successfully',
            'attorney' => $attorney
        ], 201);
    }

    /**
     * Display the specified attorney.
     */
    public function show(Attorney $attorney): JsonResponse
    {
        $attorney->load(['state', 'practiceAreas', 'clients']);
        
        return response()->json($attorney);
    }

    /**
     * Update the specified attorney.
     */
    public function update(Request $request, Attorney $attorney): JsonResponse
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('attorneys')->ignore($attorney->id)],
            'phone' => 'nullable|string|max:20',
            'license_number' => ['nullable', 'string', 'max:50', Rule::unique('attorneys')->ignore($attorney->id)],
            'state_id' => 'required|exists:states,id',
            'status' => 'required|in:active,inactive',
            'bio' => 'nullable|string',
            'years_experience' => 'nullable|integer|min:0',
            'bar_admission_date' => 'nullable|date',
            'practice_area_ids' => 'nullable|array',
            'practice_area_ids.*' => 'exists:practice_areas,id',
        ]);

        $attorney->update($validated);

        // Sync practice areas if provided
        if (isset($validated['practice_area_ids'])) {
            $attorney->practiceArea()->sync($validated['practice_area_ids']);
        }

        $attorney->load(['state', 'practiceAreas']);

        return response()->json([
            'message' => 'Attorney updated successfully',
            'attorney' => $attorney
        ]);
    }

    /**
     * Remove the specified attorney.
     */
    public function destroy(Attorney $attorney): JsonResponse
    {
        // Check if attorney has active clients
        if ($attorney->clients()->where('status', 'active')->exists()) {
            return response()->json([
                'message' => 'Cannot delete attorney with active clients'
            ], 422);
        }

        $attorney->practiceArea()->detach();
        $attorney->delete();

        return response()->json([
            'message' => 'Attorney deleted successfully'
        ]);
    }

    /**
     * Get attorneys for dropdown/select options.
     */
    public function options(): JsonResponse
    {
        $attorneys = Attorney::active()
            ->select('id', 'first_name', 'last_name')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get()
            ->map(function ($attorney) {
                return [
                    'id' => $attorney->id,
                    'name' => $attorney->full_name,
                ];
            });

        return response()->json($attorneys);
    }
}
