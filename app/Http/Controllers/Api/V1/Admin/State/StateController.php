<?php

namespace App\Http\Controllers\Api\V1\Admin\State;

use App\Http\Controllers\Controller;
use App\Models\State;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class StateController extends Controller
{
   public function index(Request $request): JsonResponse
    {
        $query = State::withCount(['attorneys'])->with('counties','attorneys.county','attorneys');

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('abbreviation', 'like', "%{$request->search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $states = $query->paginate($perPage);

        return response()->json($states);
    }

    /**
     * Store a newly created state.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:states,name',
            'abbreviation' => 'required|string|max:2|unique:states,abbreviation',
            'status' => 'required|in:active,inactive',
        ]);

        $state = State::create($validated);

        return response()->json([
            'message' => 'State created successfully',
            'state' => $state
        ], 201);
    }

    /**
     * Display the specified state.
     */
    public function show(State $state): JsonResponse
    {
        $state->load(['attorneys', 'clients']);

        return response()->json($state);
    }

    /**
     * Update the specified state.
     */
    public function update(Request $request, State $state): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('states')->ignore($state->id)],
            'abbreviation' => ['required', 'string', 'max:2', Rule::unique('states')->ignore($state->id)],
            'status' => 'required|in:active,inactive',
        ]);

        $state->update($validated);

        return response()->json([
            'message' => 'State updated successfully',
            'state' => $state
        ]);
    }

    /**
     * Remove the specified state.
     */
    public function destroy(State $state): JsonResponse
    {
        // Check if state has attorneys or clients
        if ($state->attorneys()->exists() || $state->clients()->exists()) {
            return response()->json([
                'message' => 'Cannot delete state that has attorneys or clients assigned'
            ], 422);
        }

        $state->delete();

        return response()->json([
            'message' => 'State deleted successfully'
        ]);
    }

    /**
     * Get states for dropdown/select options.
     */
    public function options(): JsonResponse
    {
        $states = State::where('status', 'active')
            ->select('id', 'name', 'abbreviation')
            ->orderBy('name')
            ->get();

        return response()->json($states);
    }

    /**
     * Get attorneys by state.
     */
    public function attorneys(State $state): JsonResponse
    {
        $attorneys = $state->attorneys()
            ->with('practiceAreas')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();

        return response()->json($attorneys);
    }

    /**
     * Get clients by state.
     */
    public function clients(State $state): JsonResponse
    {
        $clients = $state->clients()
            ->with('attorney')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();

        return response()->json($clients);
    }
}
