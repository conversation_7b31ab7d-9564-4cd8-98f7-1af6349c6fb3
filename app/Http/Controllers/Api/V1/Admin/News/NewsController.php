<?php

namespace App\Http\Controllers\Api\V1\Admin\News;

use App\Http\Controllers\Controller;
use App\Http\Requests\NewsRequest;
use App\Http\Resources\NewsResource;
use App\Traits\Pagination;
use \Illuminate\Http\JsonResponse;
use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    use Pagination;
    public function store(NewsRequest $request): JsonResponse
    {
        $news = News::create($request->all());
        return successDataResponse(NewsResource::make($news));
    }

    public function index(Request $request): JsonResponse
    {
        $take = $request->page_size ?: 10;
        $news = News::paginate($take);
        $paginatedData = $this->paginatedData($news, $request);

        return successPaginationResponse($paginatedData);
    }

    public function show($id): JsonResponse
    {
        try {
            $news = News::find($id);
            return successDataResponse($news);
        } catch (\Exception $e) {
            return failedResponse(trans('response.newsNotFound'));
        }
    }

    public function update(NewsRequest $request): JsonResponse
    {
        try {
            $news = News::findOrFail($request->id);
            $news->fill($request->all())->save();
            return successDataResponse(NewsResource::make($news));
        } catch (\Exception $e) {
            return failedResponse(trans('response.failedUpdatingNews'));
        }
    }

    public function destroy($id): JsonResponse
    {

        try {
            $news = News::find($id);
            $news->delete();
            return successResponse(trans('response.newsDeleted'));
        } catch (\Exception $e) {
            return failedResponse(trans('response.newsNotFound'));
        }
    }
}
