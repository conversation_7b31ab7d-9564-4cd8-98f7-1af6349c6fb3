<?php

namespace App\Http\Controllers\Api\V1\Admin\PracticeArea;

use App\Http\Controllers\Controller;
use App\Models\PracticeArea;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class PracticeAreaController extends Controller
{
    /**
     * Display a listing of practice areas.
     */
    public function index(Request $request): JsonResponse
    {
        $query = PracticeArea::withCount('attorneys');

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('description', 'like', "%{$request->search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $practiceAreas = $query->paginate($perPage);
        return response()->json($practiceAreas);
    }

    /**
     * Store a newly created practice area.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:practice_areas,name',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive',
        ]);

        $practiceArea = PracticeArea::create($validated);

        return response()->json([
            'message' => 'Practice area created successfully',
            'practice_area' => $practiceArea
        ], 201);
    }

    /**
     * Display the specified practice area.
     */
    public function show(PracticeArea $practiceArea): JsonResponse
    {
        $practiceArea->load(['attorneys']);

        return response()->json($practiceArea);
    }

    /**
     * Update the specified practice area.
     */
    public function update(Request $request, PracticeArea $practiceArea): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('practice_areas')->ignore($practiceArea->id)],
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive',
        ]);

        $practiceArea->update($validated);

        return response()->json([
            'message' => 'Practice area updated successfully',
            'practice_area' => $practiceArea
        ]);
    }

    /**
     * Remove the specified practice area.
     */
    public function destroy(PracticeArea $practiceArea): JsonResponse
    {
        // Check if practice area has attorneys
        if ($practiceArea->attorneys()->exists()) {
            return response()->json([
                'message' => 'Cannot delete practice area that has attorneys assigned'
            ], 422);
        }

        $practiceArea->delete();

        return response()->json([
            'message' => 'Practice area deleted successfully'
        ]);
    }

    /**
     * Get practice areas for dropdown/select options.
     */
    public function options(): JsonResponse
    {
        $practiceAreas = PracticeArea::where('status', 'active')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return response()->json($practiceAreas);
    }

    /**
     * Get attorneys by practice area.
     */
    public function attorneys(PracticeArea $practiceArea): JsonResponse
    {
        $attorneys = $practiceArea->attorneys()
            ->with('state')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();

        return response()->json($attorneys);
    }
}
