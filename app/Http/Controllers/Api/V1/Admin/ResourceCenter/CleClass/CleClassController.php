<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\CleClass;

use App\Http\Controllers\Controller;
use App\Models\CleClass;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class CleClassController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = CleClass::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('instructor', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        if ($request->filled('category')) {
            $query->where('category', $request->get('category'));
        }

        if ($request->filled('date_from')) {
            $query->where('date', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('date', '<=', $request->get('date_to'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'date');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 25);
        $classes = $query->paginate($perPage);

        return response()->json([
            'data' => $classes->items(),
            'meta' => [
                'current_page' => $classes->currentPage(),
                'last_page' => $classes->lastPage(),
                'per_page' => $classes->perPage(),
                'total' => $classes->total(),
            ],
            'stats' => [
                'total' => CleClass::count(),
                'scheduled' => CleClass::where('status', 'scheduled')->count(),
                'total_enrolled' => CleClass::sum('enrolled'),
                'total_credits' => CleClass::sum('credits'),
            ],
            'options' => [
                'types' => CleClass::getTypes(),
                'statuses' => CleClass::getStatuses(),
                'categories' => CleClass::getCategories(),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructor' => 'required|string|max:255',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required|date_format:H:i',
            'duration' => 'required|integer|min:1',
            'location' => 'required|string|max:255',
            'type' => ['required', Rule::in(CleClass::getTypes())],
            'credits' => 'required|integer|min:1',
            'capacity' => 'required|integer|min:1|max:1000',
            'price' => 'required|numeric|min:0|max:9999.99',
            'category' => ['required', Rule::in(CleClass::getCategories())],
            'requirements' => 'nullable|array',
            'meeting_url' => 'nullable|url',
            'notes' => 'nullable|string',
        ]);

        $cleClass = CleClass::create($validated);

        return response()->json([
            'message' => 'CLE Class created successfully',
            'class' => $cleClass
        ], 201);
    }

    public function show(CleClass $cleClass): JsonResponse
    {
        return response()->json([
            'class' => $cleClass->load([])
        ]);
    }

    public function update(Request $request, CleClass $cleClass): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'instructor' => 'sometimes|required|string|max:255',
            'date' => 'sometimes|required|date',
            'time' => 'sometimes|required|date_format:H:i',
            'duration' => 'sometimes|required|integer|min:1',
            'location' => 'sometimes|required|string|max:255',
            'type' => ['sometimes', 'required', Rule::in(CleClass::getTypes())],
            'credits' => 'sometimes|required|integer|min:1',
            'capacity' => 'sometimes|required|integer|min:1|max:1000',
            'enrolled' => 'sometimes|required|integer|min:0',
            'status' => ['sometimes', 'required', Rule::in(CleClass::getStatuses())],
            'price' => 'sometimes|required|numeric|min:0|max:9999.99',
            'category' => ['sometimes', 'required', Rule::in(CleClass::getCategories())],
            'requirements' => 'nullable|array',
            'meeting_url' => 'nullable|url',
            'notes' => 'nullable|string',
            'is_active' => 'sometimes|boolean',
        ]);

        // Validate enrolled doesn't exceed capacity
        if (isset($validated['enrolled']) && isset($validated['capacity'])) {
            if ($validated['enrolled'] > $validated['capacity']) {
                return response()->json([
                    'message' => 'Enrolled count cannot exceed capacity',
                    'errors' => ['enrolled' => ['Enrolled count cannot exceed capacity']]
                ], 422);
            }
        } elseif (isset($validated['enrolled']) && $validated['enrolled'] > $cleClass->capacity) {
            return response()->json([
                'message' => 'Enrolled count cannot exceed capacity',
                'errors' => ['enrolled' => ['Enrolled count cannot exceed capacity']]
            ], 422);
        } elseif (isset($validated['capacity']) && $cleClass->enrolled > $validated['capacity']) {
            return response()->json([
                'message' => 'Capacity cannot be less than current enrollment',
                'errors' => ['capacity' => ['Capacity cannot be less than current enrollment']]
            ], 422);
        }

        $cleClass->update($validated);

        return response()->json([
            'message' => 'CLE Class updated successfully',
            'class' => $cleClass->fresh()
        ]);
    }

    public function destroy(CleClass $cleClass): JsonResponse
    {
        // Check if class has enrollments
        if ($cleClass->enrolled > 0) {
            return response()->json([
                'message' => 'Cannot delete class with existing enrollments',
                'error' => 'Class has enrolled students'
            ], 422);
        }

        $cleClass->delete();

        return response()->json([
            'message' => 'CLE Class deleted successfully'
        ]);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate,update_status',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:cle_classes,id',
            'status' => ['required_if:action,update_status', Rule::in(CleClass::getStatuses())],
        ]);

        $classes = CleClass::whereIn('id', $validated['ids']);

        switch ($validated['action']) {
            case 'delete':
                // Check for enrollments
                $classesWithEnrollments = $classes->where('enrolled', '>', 0)->count();
                if ($classesWithEnrollments > 0) {
                    return response()->json([
                        'message' => 'Cannot delete classes with existing enrollments',
                        'error' => "{$classesWithEnrollments} classes have enrolled students"
                    ], 422);
                }
                $classes->delete();
                $message = 'Classes deleted successfully';
                break;

            case 'activate':
                $classes->update(['is_active' => true]);
                $message = 'Classes activated successfully';
                break;

            case 'deactivate':
                $classes->update(['is_active' => false]);
                $message = 'Classes deactivated successfully';
                break;

            case 'update_status':
                $classes->update(['status' => $validated['status']]);
                $message = 'Classes status updated successfully';
                break;
        }

        return response()->json(['message' => $message]);
    }

    public function enroll(Request $request, CleClass $cleClass): JsonResponse
    {
        if (!$cleClass->canEnroll()) {
            return response()->json([
                'message' => 'Cannot enroll in this class',
                'error' => 'Class is full, inactive, or not scheduled'
            ], 422);
        }

        if ($cleClass->incrementEnrollment()) {
            return response()->json([
                'message' => 'Successfully enrolled in class',
                'class' => $cleClass->fresh()
            ]);
        }

        return response()->json([
            'message' => 'Failed to enroll in class',
            'error' => 'Enrollment failed'
        ], 422);
    }

    public function unenroll(Request $request, CleClass $cleClass): JsonResponse
    {
        if ($cleClass->decrementEnrollment()) {
            return response()->json([
                'message' => 'Successfully unenrolled from class',
                'class' => $cleClass->fresh()
            ]);
        }

        return response()->json([
            'message' => 'Failed to unenroll from class',
            'error' => 'No enrollments to remove'
        ], 422);
    }
}
