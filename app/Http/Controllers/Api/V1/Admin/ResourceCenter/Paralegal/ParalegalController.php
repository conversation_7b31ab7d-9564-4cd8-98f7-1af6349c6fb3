<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\Paralegal;

use App\Http\Controllers\Controller;
use App\Models\Paralegal;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class ParalegalController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Paralegal::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('state', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($request->filled('availability')) {
            $query->where('availability', $request->get('availability'));
        }

        if ($request->filled('employment_type')) {
            $query->where('employment_type', $request->get('employment_type'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('specialization')) {
            $query->whereJsonContains('specializations', $request->get('specialization'));
        }

        if ($request->filled('city')) {
            $query->where('city', 'like', '%' . $request->get('city') . '%');
        }

        if ($request->filled('state')) {
            $query->where('state', 'like', '%' . $request->get('state') . '%');
        }

        if ($request->filled('min_rating')) {
            $query->where('rating', '>=', $request->get('min_rating'));
        }

        if ($request->filled('max_rate')) {
            $query->where('hourly_rate', '<=', $request->get('max_rate'));
        }

        if ($request->filled('min_experience')) {
            $query->where('experience_years', '>=', $request->get('min_experience'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 25);
        $paralegals = $query->paginate($perPage);

        return response()->json([
            'data' => $paralegals->items(),
            'meta' => [
                'current_page' => $paralegals->currentPage(),
                'last_page' => $paralegals->lastPage(),
                'per_page' => $paralegals->perPage(),
                'total' => $paralegals->total(),
            ],
            'stats' => [
                'total' => Paralegal::count(),
                'available' => Paralegal::where('availability', 'available')->count(),
                'specializations' => collect(Paralegal::pluck('specializations')->flatten())->unique()->count(),
                'average_rating' => Paralegal::where('total_jobs', '>', 0)->avg('rating'),
            ],
            'options' => [
                'availability' => ['available', 'busy', 'unavailable'],
                'employment_types' => ['full-time', 'part-time', 'contract', 'freelance'],
                'statuses' => ['active', 'inactive', 'pending'],
                'specializations' => $this->getSpecializationOptions(),
                'certifications' => $this->getCertificationOptions(),
                'skills' => $this->getSkillOptions(),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:paralegals,email',
            'phone' => 'required|string|max:20',
            'address' => 'nullable|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:50',
            'zip_code' => 'nullable|string|max:10',
            'specializations' => 'required|array|min:1',
            'specializations.*' => 'required|string',
            'experience_years' => 'required|integer|min:0|max:50',
            'education' => 'required|array|min:1',
            'education.*' => 'required|string',
            'certifications' => 'nullable|array',
            'certifications.*' => 'required|string',
            'employment_type' => 'required|in:full-time,part-time,contract,freelance',
            'hourly_rate' => 'required|numeric|min:0|max:999.99',
            'skills' => 'required|array|min:1',
            'skills.*' => 'required|string',
            'bio' => 'nullable|string',
            'resume_url' => 'nullable|url',
            'linkedin' => 'nullable|url',
            'software_proficiency' => 'nullable|array',
            'remote_work_available' => 'boolean',
            'travel_available' => 'boolean',
            'languages_spoken' => 'nullable|array',
            'bar_admission' => 'nullable|string|max:100',
            'notary_public' => 'boolean',
        ]);

        $validated['joined_date'] = now()->toDateString();
        $validated['last_active'] = now();

        $paralegal = Paralegal::create($validated);

        return response()->json([
            'message' => 'Paralegal created successfully',
            'paralegal' => $paralegal
        ], 201);
    }

    public function show(Paralegal $paralegal): JsonResponse
    {
        return response()->json([
            'paralegal' => $paralegal
        ]);
    }

    public function update(Request $request, Paralegal $paralegal): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:paralegals,email,' . $paralegal->id,
            'phone' => 'sometimes|required|string|max:20',
            'address' => 'nullable|string',
            'city' => 'sometimes|required|string|max:100',
            'state' => 'sometimes|required|string|max:50',
            'zip_code' => 'nullable|string|max:10',
            'specializations' => 'sometimes|required|array|min:1',
            'specializations.*' => 'required|string',
            'experience_years' => 'sometimes|required|integer|min:0|max:50',
            'education' => 'sometimes|required|array|min:1',
            'education.*' => 'required|string',
            'certifications' => 'nullable|array',
            'certifications.*' => 'required|string',
            'availability' => 'sometimes|required|in:available,busy,unavailable',
            'employment_type' => 'sometimes|required|in:full-time,part-time,contract,freelance',
            'hourly_rate' => 'sometimes|required|numeric|min:0|max:999.99',
            'skills' => 'sometimes|required|array|min:1',
            'skills.*' => 'required|string',
            'status' => 'sometimes|required|in:active,inactive,pending',
            'bio' => 'nullable|string',
            'resume_url' => 'nullable|url',
            'linkedin' => 'nullable|url',
            'software_proficiency' => 'nullable|array',
            'remote_work_available' => 'sometimes|boolean',
            'travel_available' => 'sometimes|boolean',
            'languages_spoken' => 'nullable|array',
            'bar_admission' => 'nullable|string|max:100',
            'notary_public' => 'sometimes|boolean',
            'is_active' => 'sometimes|boolean',
        ]);

        $paralegal->update($validated);

        return response()->json([
            'message' => 'Paralegal updated successfully',
            'paralegal' => $paralegal->fresh()
        ]);
    }

    public function destroy(Paralegal $paralegal): JsonResponse
    {
        $paralegal->delete();

        return response()->json([
            'message' => 'Paralegal deleted successfully'
        ]);
    }

    public function updateAvailability(Request $request, Paralegal $paralegal): JsonResponse
    {
        $validated = $request->validate([
            'availability' => 'required|in:available,busy,unavailable'
        ]);

        $paralegal->update([
            'availability' => $validated['availability'],
            'last_active' => now()
        ]);

        return response()->json([
            'message' => 'Availability updated successfully',
            'paralegal' => $paralegal->fresh()
        ]);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate,update_status,update_availability',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:paralegals,id',
            'status' => 'required_if:action,update_status|in:active,inactive,pending',
            'availability' => 'required_if:action,update_availability|in:available,busy,unavailable',
        ]);

        $paralegals = Paralegal::whereIn('id', $validated['ids']);

        switch ($validated['action']) {
            case 'delete':
                $paralegals->delete();
                $message = 'Paralegals deleted successfully';
                break;

            case 'activate':
                $paralegals->update(['is_active' => true]);
                $message = 'Paralegals activated successfully';
                break;

            case 'deactivate':
                $paralegals->update(['is_active' => false]);
                $message = 'Paralegals deactivated successfully';
                break;

            case 'update_status':
                $paralegals->update(['status' => $validated['status']]);
                $message = 'Paralegals status updated successfully';
                break;

            case 'update_availability':
                $paralegals->update([
                    'availability' => $validated['availability'],
                    'last_active' => now()
                ]);
                $message = 'Paralegals availability updated successfully';
                break;
        }

        return response()->json(['message' => $message]);
    }

    private function getSpecializationOptions(): array
    {
        return [
            'Corporate Law', 'Criminal Law', 'Family Law', 'Real Estate Law',
            'Immigration Law', 'Employment Law', 'Intellectual Property',
            'Personal Injury', 'Estate Planning', 'Bankruptcy Law',
            'Environmental Law', 'Tax Law', 'Administrative Law'
        ];
    }

    private function getCertificationOptions(): array
    {
        return [
            'NALA Certified', 'NFPA Certified', 'California Advanced Paralegal',
            'Immigration Law Specialist', 'Real Estate Law Specialist',
            'Corporate Law Certified', 'Litigation Specialist'
        ];
    }

    private function getSkillOptions(): array
    {
        return [
            'Legal Research', 'Document Drafting', 'Case Management',
            'E-Discovery', 'Client Interviews', 'Court Filing',
            'Contract Review', 'Deposition Preparation', 'Trial Preparation',
            'Database Management', 'Legal Writing', 'Notary Services'
        ];
    }
}
