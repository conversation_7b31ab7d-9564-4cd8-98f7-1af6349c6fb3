<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\Bondsman;

use App\Http\Controllers\Controller;
use App\Models\Bondsman;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class BondsmanController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Bondsman::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('company_name', 'like', "%{$search}%")
                  ->orWhere('contact_person', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('state', 'like', "%{$search}%")
                  ->orWhere('license_number', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($request->filled('availability')) {
            $query->where('availability', $request->get('availability'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('service_area')) {
            $query->whereJsonContains('service_areas', $request->get('service_area'));
        }

        if ($request->filled('bond_type')) {
            $query->whereJsonContains('bond_types', $request->get('bond_type'));
        }

        if ($request->filled('city')) {
            $query->where('city', 'like', '%' . $request->get('city') . '%');
        }

        if ($request->filled('state')) {
            $query->where('state', 'like', '%' . $request->get('state') . '%');
        }

        if ($request->filled('min_rating')) {
            $query->where('rating', '>=', $request->get('min_rating'));
        }

        if ($request->filled('max_fee')) {
            $query->where('fee_percentage', '<=', $request->get('max_fee'));
        }

        if ($request->filled('min_bond')) {
            $query->where('maximum_bond', '>=', $request->get('min_bond'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'company_name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 25);
        $bondsmen = $query->paginate($perPage);

        return response()->json([
            'data' => $bondsmen->items(),
            'meta' => [
                'current_page' => $bondsmen->currentPage(),
                'last_page' => $bondsmen->lastPage(),
                'per_page' => $bondsmen->perPage(),
                'total' => $bondsmen->total(),
            ],
            'stats' => [
                'total' => Bondsman::count(),
                'available_24_7' => Bondsman::where('availability', '24/7')->count(),
                'service_areas' => collect(Bondsman::pluck('service_areas')->flatten())->unique()->count(),
                'total_bonds' => Bondsman::sum('total_bonds'),
            ],
            'options' => [
                'availability' => ['24/7', 'business-hours', 'on-call'],
                'statuses' => ['active', 'inactive', 'suspended'],
                'bond_types' => $this->getBondTypeOptions(),
                'payment_methods' => $this->getPaymentMethodOptions(),
                'collateral_types' => $this->getCollateralTypeOptions(),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'contact_person' => 'required|string|max:255',
            'email' => 'required|email|unique:bondsmen,email',
            'phone' => 'required|string|max:20',
            'emergency_phone' => 'nullable|string|max:20',
            'website' => 'nullable|url',
            'address' => 'nullable|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:50',
            'zip_code' => 'nullable|string|max:10',
            'service_areas' => 'required|array|min:1',
            'service_areas.*' => 'required|string',
            'bond_types' => 'required|array|min:1',
            'bond_types.*' => 'required|string',
            'minimum_bond' => 'required|numeric|min:0|max:*********.99',
            'maximum_bond' => 'required|numeric|min:0|max:*********999.99',
            'fee_percentage' => 'required|numeric|min:0|max:100',
            'availability' => 'required|in:24/7,business-hours,on-call',
            'license_number' => 'required|string|max:50|unique:bondsmen,license_number',
            'insurance_amount' => 'required|numeric|min:0|max:*********999.99',
            'description' => 'nullable|string',
            'accepted_payment_methods' => 'nullable|array',
            'collateral_accepted' => 'boolean',
            'collateral_types' => 'nullable|array',
            'business_hours' => 'nullable|string|max:100',
            'mobile_service' => 'boolean',
            'mobile_service_fee' => 'nullable|numeric|min:0|max:999.99',
            'license_expiry' => 'nullable|date|after:today',
            'bonding_company' => 'nullable|string|max:255',
        ]);

        // Validate minimum_bond <= maximum_bond
        if ($validated['minimum_bond'] > $validated['maximum_bond']) {
            return response()->json([
                'message' => 'Minimum bond cannot be greater than maximum bond',
                'errors' => ['minimum_bond' => ['Minimum bond cannot be greater than maximum bond']]
            ], 422);
        }

        $validated['joined_date'] = now()->toDateString();
        $validated['last_active'] = now();

        $bondsman = Bondsman::create($validated);

        return response()->json([
            'message' => 'Bondsman created successfully',
            'bondsman' => $bondsman
        ], 201);
    }

    public function show(Bondsman $bondsman): JsonResponse
    {
        return response()->json([
            'bondsman' => $bondsman
        ]);
    }

    public function update(Request $request, Bondsman $bondsman): JsonResponse
    {
        $validated = $request->validate([
            'company_name' => 'sometimes|required|string|max:255',
            'contact_person' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:bondsmen,email,' . $bondsman->id,
            'phone' => 'sometimes|required|string|max:20',
            'emergency_phone' => 'nullable|string|max:20',
            'website' => 'nullable|url',
            'address' => 'nullable|string',
            'city' => 'sometimes|required|string|max:100',
            'state' => 'sometimes|required|string|max:50',
            'zip_code' => 'nullable|string|max:10',
            'service_areas' => 'sometimes|required|array|min:1',
            'service_areas.*' => 'required|string',
            'bond_types' => 'sometimes|required|array|min:1',
            'bond_types.*' => 'required|string',
            'minimum_bond' => 'sometimes|required|numeric|min:0|max:*********.99',
            'maximum_bond' => 'sometimes|required|numeric|min:0|max:*********999.99',
            'fee_percentage' => 'sometimes|required|numeric|min:0|max:100',
            'availability' => 'sometimes|required|in:24/7,business-hours,on-call',
            'license_number' => 'sometimes|required|string|max:50|unique:bondsmen,license_number,' . $bondsman->id,
            'insurance_amount' => 'sometimes|required|numeric|min:0|max:*********999.99',
            'status' => 'sometimes|required|in:active,inactive,suspended',
            'description' => 'nullable|string',
            'accepted_payment_methods' => 'nullable|array',
            'collateral_accepted' => 'sometimes|boolean',
            'collateral_types' => 'nullable|array',
            'business_hours' => 'nullable|string|max:100',
            'mobile_service' => 'sometimes|boolean',
            'mobile_service_fee' => 'nullable|numeric|min:0|max:999.99',
            'license_expiry' => 'nullable|date|after:today',
            'bonding_company' => 'nullable|string|max:255',
            'is_active' => 'sometimes|boolean',
        ]);

        // Validate minimum_bond <= maximum_bond
        $minBond = $validated['minimum_bond'] ?? $bondsman->minimum_bond;
        $maxBond = $validated['maximum_bond'] ?? $bondsman->maximum_bond;

        if ($minBond > $maxBond) {
            return response()->json([
                'message' => 'Minimum bond cannot be greater than maximum bond',
                'errors' => ['minimum_bond' => ['Minimum bond cannot be greater than maximum bond']]
            ], 422);
        }

        $bondsman->update($validated);

        return response()->json([
            'message' => 'Bondsman updated successfully',
            'bondsman' => $bondsman->fresh()
        ]);
    }

    public function destroy(Bondsman $bondsman): JsonResponse
    {
        $bondsman->delete();

        return response()->json([
            'message' => 'Bondsman deleted successfully'
        ]);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate,update_status',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:bondsmen,id',
            'status' => 'required_if:action,update_status|in:active,inactive,suspended',
        ]);

        $bondsmen = Bondsman::whereIn('id', $validated['ids']);

        switch ($validated['action']) {
            case 'delete':
                $bondsmen->delete();
                $message = 'Bondsmen deleted successfully';
                break;

            case 'activate':
                $bondsmen->update(['is_active' => true]);
                $message = 'Bondsmen activated successfully';
                break;

            case 'deactivate':
                $bondsmen->update(['is_active' => false]);
                $message = 'Bondsmen deactivated successfully';
                break;

            case 'update_status':
                $bondsmen->update(['status' => $validated['status']]);
                $message = 'Bondsmen status updated successfully';
                break;
        }

        return response()->json(['message' => $message]);
    }

    private function getBondTypeOptions(): array
    {
        return [
            'Felony', 'Misdemeanor', 'Traffic', 'DUI', 'Domestic Violence',
            'Immigration', 'Federal', 'Appeal', 'Probation Violation',
            'Warrant', 'Juvenile', 'Civil'
        ];
    }

    private function getPaymentMethodOptions(): array
    {
        return [
            'Cash', 'Credit Card', 'Debit Card', 'Check', 'Money Order',
            'Bank Transfer', 'Payment Plan', 'Collateral'
        ];
    }

    private function getCollateralTypeOptions(): array
    {
        return [
            'Real Estate', 'Vehicles', 'Jewelry', 'Electronics',
            'Bank Accounts', 'Stocks/Bonds', 'Business Assets',
            'Personal Property'
        ];
    }
}
