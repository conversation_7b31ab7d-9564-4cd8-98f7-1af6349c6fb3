<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumCategory;

use App\Http\Controllers\Controller;
use App\Models\ForumCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class ForumCategoryController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = ForumCategory::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->filled('color')) {
            $query->where('color', $request->get('color'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 25);
        $categories = $query->paginate($perPage);

        return response()->json([
            'data' => $categories->items(),
            'meta' => [
                'current_page' => $categories->currentPage(),
                'last_page' => $categories->lastPage(),
                'per_page' => $categories->perPage(),
                'total' => $categories->total(),
            ],
            'stats' => [
                'total' => ForumCategory::count(),
                'active' => ForumCategory::where('is_active', true)->count(),
                'total_posts' => ForumCategory::sum('post_count'),
            ],
            'options' => [
                'colors' => $this->getColorOptions(),
                'icons' => $this->getIconOptions(),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:forum_categories,name',
            'description' => 'nullable|string',
            'color' => 'required|in:' . implode(',', array_keys($this->getColorOptions())),
            'icon' => 'nullable|in:' . implode(',', $this->getIconOptions()),
            'sort_order' => 'nullable|integer|min:0',
            'requires_approval' => 'boolean',
            'allowed_roles' => 'nullable|array',
        ]);

        // Generate slug from name
        $validated['slug'] = Str::slug($validated['name']);

        // Ensure unique slug
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (ForumCategory::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Set sort order if not provided
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = ForumCategory::max('sort_order') + 1;
        }

        $category = ForumCategory::create($validated);

        return response()->json([
            'message' => 'Forum category created successfully',
            'category' => $category
        ], 201);
    }

    public function show(ForumCategory $forumCategory): JsonResponse
    {
        return response()->json([
            'category' => $forumCategory
        ]);
    }

    public function update(Request $request, ForumCategory $forumCategory): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255|unique:forum_categories,name,' . $forumCategory->id,
            'description' => 'nullable|string',
            'color' => 'sometimes|required|in:' . implode(',', array_keys($this->getColorOptions())),
            'icon' => 'nullable|in:' . implode(',', $this->getIconOptions()),
            'sort_order' => 'sometimes|nullable|integer|min:0',
            'is_active' => 'sometimes|boolean',
            'requires_approval' => 'sometimes|boolean',
            'allowed_roles' => 'nullable|array',
        ]);

        // Update slug if name changed
        if (isset($validated['name']) && $validated['name'] !== $forumCategory->name) {
            $slug = Str::slug($validated['name']);

            // Ensure unique slug
            $originalSlug = $slug;
            $counter = 1;
            while (ForumCategory::where('slug', $slug)->where('id', '!=', $forumCategory->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $validated['slug'] = $slug;
        }

        $forumCategory->update($validated);

        return response()->json([
            'message' => 'Forum category updated successfully',
            'category' => $forumCategory->fresh()
        ]);
    }

    public function destroy(ForumCategory $forumCategory): JsonResponse
    {
        // Check if category has posts
        if ($forumCategory->post_count > 0) {
            return response()->json([
                'message' => 'Cannot delete category with existing posts',
                'error' => 'Category has posts'
            ], 422);
        }

        $forumCategory->delete();

        return response()->json([
            'message' => 'Forum category deleted successfully'
        ]);
    }

    public function reorder(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:forum_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['categories'] as $categoryData) {
            ForumCategory::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json([
            'message' => 'Categories reordered successfully'
        ]);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:forum_categories,id',
        ]);

        $categories = ForumCategory::whereIn('id', $validated['ids']);

        switch ($validated['action']) {
            case 'delete':
                // Check for categories with posts
                $categoriesWithPosts = $categories->where('post_count', '>', 0)->count();
                if ($categoriesWithPosts > 0) {
                    return response()->json([
                        'message' => 'Cannot delete categories with existing posts',
                        'error' => "{$categoriesWithPosts} categories have posts"
                    ], 422);
                }
                $categories->delete();
                $message = 'Categories deleted successfully';
                break;

            case 'activate':
                $categories->update(['is_active' => true]);
                $message = 'Categories activated successfully';
                break;

            case 'deactivate':
                $categories->update(['is_active' => false]);
                $message = 'Categories deactivated successfully';
                break;
        }

        return response()->json(['message' => $message]);
    }

    private function getColorOptions(): array
    {
        return [
            'blue' => 'Blue',
            'green' => 'Green',
            'red' => 'Red',
            'yellow' => 'Yellow',
            'purple' => 'Purple',
            'pink' => 'Pink',
            'indigo' => 'Indigo',
            'gray' => 'Gray',
            'orange' => 'Orange',
            'teal' => 'Teal',
        ];
    }

    private function getIconOptions(): array
    {
        return [
            'MessageSquare',
            'Scale',
            'Lightbulb',
            'Users',
            'BookOpen',
            'Briefcase',
            'Gavel',
            'FileText',
            'HelpCircle',
            'Settings',
            'Star',
            'Shield',
            'Award',
            'Target',
            'Zap'
        ];
    }
}
