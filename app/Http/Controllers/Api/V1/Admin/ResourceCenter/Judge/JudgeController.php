<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\Judge;

use App\Http\Controllers\Controller;
use App\Models\Judge;
use App\Models\JudgeReview;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class JudgeController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Judge::with(['reviews' => function ($q) {
            $q->latest()->limit(5);
        }]);

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('court', 'like', "%{$search}%")
                  ->orWhere('jurisdiction', 'like', "%{$search}%")
                  ->orWhere('position', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('court')) {
            $query->where('court', 'like', '%' . $request->get('court') . '%');
        }

        if ($request->filled('jurisdiction')) {
            $query->where('jurisdiction', 'like', '%' . $request->get('jurisdiction') . '%');
        }

        if ($request->filled('min_rating')) {
            $query->where('average_rating', '>=', $request->get('min_rating'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 25);
        $judges = $query->paginate($perPage);

        return response()->json([
            'judges' => $judges,
            'filters' => $request->only(['search', 'status', 'court', 'jurisdiction', 'min_rating']),
            'stats' => [
                'total' => Judge::count(),
                'total_ratings' => JudgeReview::approved()->count(),
                'average_rating' => Judge::where('total_ratings', '>', 0)->avg('average_rating'),
                'under_review' => Judge::where('status', 'under_review')->count(),
            ],
            'options' => [
                'statuses' => Judge::getStatuses(),
                'positions' => Judge::getPositions(),
                'specializations' => Judge::getSpecializations(),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'court' => 'required|string|max:255',
            'jurisdiction' => 'required|string|max:255',
            'position' => ['nullable', Rule::in(Judge::getPositions())],
            'bio' => 'nullable|string',
            'photo_url' => 'nullable|url',
            'specializations' => 'nullable|array',
            'specializations.*' => Rule::in(Judge::getSpecializations()),
        ]);

        $judge = Judge::create($validated);

        return response()->json([
            'message' => 'Judge created successfully',
            'judge' => $judge
        ], 201);
    }

    public function show(Judge $judge): JsonResponse
    {
        $judge->load([
            'reviews' => function ($query) {
                $query->latest()->with('reviewer');
            }
        ]);

        return response()->json([
            'judge' => $judge,
            'review_stats' => [
                'total' => $judge->reviews->count(),
                'approved' => $judge->approvedReviews->count(),
                'pending' => $judge->pendingReviews->count(),
                'flagged' => $judge->flaggedReviews->count(),
                'by_rating' => $judge->reviews->groupBy('overall_rating')->map->count(),
            ]
        ]);
    }

    public function update(Request $request, Judge $judge): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'court' => 'sometimes|required|string|max:255',
            'jurisdiction' => 'sometimes|required|string|max:255',
            'position' => ['nullable', Rule::in(Judge::getPositions())],
            'bio' => 'nullable|string',
            'status' => ['sometimes', 'required', Rule::in(Judge::getStatuses())],
            'photo_url' => 'nullable|url',
            'specializations' => 'nullable|array',
            'specializations.*' => Rule::in(Judge::getSpecializations()),
            'is_active' => 'sometimes|boolean',
        ]);

        $judge->update($validated);

        return response()->json([
            'message' => 'Judge updated successfully',
            'judge' => $judge->fresh()
        ]);
    }

    public function destroy(Judge $judge): JsonResponse
    {
        // Check if judge has reviews
        if ($judge->reviews()->count() > 0) {
            return response()->json([
                'message' => 'Cannot delete judge with existing reviews',
                'error' => 'Judge has review history'
            ], 422);
        }

        $judge->delete();

        return response()->json([
            'message' => 'Judge deleted successfully'
        ]);
    }

    public function reviews(Judge $judge, Request $request): JsonResponse
    {
        $query = $judge->reviews()->with('reviewer');

        // Filters
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('rating')) {
            $query->where('overall_rating', $request->get('rating'));
        }

        if ($request->filled('case_type')) {
            $query->where('case_type', $request->get('case_type'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $reviews = $query->paginate($request->get('per_page', 25));

        return response()->json([
            'reviews' => $reviews,
            'stats' => [
                'total' => $judge->reviews->count(),
                'approved' => $judge->approvedReviews->count(),
                'pending' => $judge->pendingReviews->count(),
                'flagged' => $judge->flaggedReviews->count(),
            ]
        ]);
    }

    public function approveReview(Request $request, Judge $judge, JudgeReview $review): JsonResponse
    {
        if ($review->judge_id !== $judge->id) {
            return response()->json(['error' => 'Review does not belong to this judge'], 422);
        }

        if (!$review->canBeModerated()) {
            return response()->json(['error' => 'Review cannot be moderated'], 422);
        }

        $review->approve($request->user());

        return response()->json([
            'message' => 'Review approved successfully',
            'review' => $review->fresh(),
            'judge' => $judge->fresh()
        ]);
    }

    public function rejectReview(Request $request, Judge $judge, JudgeReview $review): JsonResponse
    {
        $validated = $request->validate([
            'reason' => 'nullable|string|max:255'
        ]);

        if ($review->judge_id !== $judge->id) {
            return response()->json(['error' => 'Review does not belong to this judge'], 422);
        }

        if (!$review->canBeModerated()) {
            return response()->json(['error' => 'Review cannot be moderated'], 422);
        }

        $review->reject($request->user(), $validated['reason'] ?? null);

        return response()->json([
            'message' => 'Review rejected successfully',
            'review' => $review->fresh()
        ]);
    }

    public function flagReview(Request $request, Judge $judge, JudgeReview $review): JsonResponse
    {
        $validated = $request->validate([
            'reason' => ['required', Rule::in(array_keys(JudgeReview::getFlagReasons()))]
        ]);

        if ($review->judge_id !== $judge->id) {
            return response()->json(['error' => 'Review does not belong to this judge'], 422);
        }

        $review->flag($validated['reason'], $request->user());

        return response()->json([
            'message' => 'Review flagged successfully',
            'review' => $review->fresh()
        ]);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate,update_status',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:judges,id',
            'status' => ['required_if:action,update_status', Rule::in(Judge::getStatuses())],
        ]);

        $judges = Judge::whereIn('id', $validated['ids']);

        switch ($validated['action']) {
            case 'delete':
                // Check for reviews
                $judgesWithReviews = $judges->has('reviews')->count();
                if ($judgesWithReviews > 0) {
                    return response()->json([
                        'message' => 'Cannot delete judges with existing reviews',
                        'error' => "{$judgesWithReviews} judges have review history"
                    ], 422);
                }
                $judges->delete();
                $message = 'Judges deleted successfully';
                break;

            case 'activate':
                $judges->update(['is_active' => true]);
                $message = 'Judges activated successfully';
                break;

            case 'deactivate':
                $judges->update(['is_active' => false]);
                $message = 'Judges deactivated successfully';
                break;

            case 'update_status':
                $judges->update(['status' => $validated['status']]);
                $message = 'Judges status updated successfully';
                break;
        }

        return response()->json(['message' => $message]);
    }
}
