<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Attorney;
use App\Models\Client;
use App\Models\PracticeArea;
use App\Models\State;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Get dashboard statistics.
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'attorneys' => User::with('state')->where('user_type', 'ATTORNEY')->count(),
            'clients' => User::with('state')->where('user_type', 'CLIENT')->count(),
            'practice_areas' => PracticeArea::count(),
            'states' => State::count(),
            'active_attorneys' => User::with('state')->where('user_type', 'ATTORNEY')->where('status', 'active')->count(),
            'active_clients' => User::with('state')->where('user_type', 'CLIENT')->where('status', 'active')->count(),
            'active_practice_areas' => PracticeArea::where('status', 'active')->count(),
            'active_states' => State::where('status', 'active')->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Get recent activity.
     */
    public function recentActivity(): JsonResponse
    {
        $activities = collect();

        // Recent attorneys
        $recentAttorneys = User::with('state')->where('user_type', 'ATTORNEY')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($attorney) {
                return [
                    'type' => 'attorney',
                    'action' => 'created',
                    'title' => "New attorney registered",
                    'description' => "{$attorney->full_name} joined the team",
                    'created_at' => $attorney->created_at,
                    'data' => $attorney
                ];
            });

        // Recent clients
        $recentClients = User::with('state')->where('user_type', 'CLIENT')->with(['attorney', 'state'])
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($client) {
                return [
                    'type' => 'client',
                    'action' => 'created',
                    'title' => "New client registered",
                    'description' => "{$client->full_name} added to system",
                    'created_at' => $client->created_at,
                    'data' => $client
                ];
            });

        // Recent practice areas
        $recentPracticeAreas = PracticeArea::latest()
            ->limit(3)
            ->get()
            ->map(function ($practiceArea) {
                return [
                    'type' => 'practice_area',
                    'action' => 'created',
                    'title' => "Practice area added",
                    'description' => "{$practiceArea->name} specialization",
                    'created_at' => $practiceArea->created_at,
                    'data' => $practiceArea
                ];
            });

        // Combine and sort by date
        $activities = $activities
            ->concat($recentAttorneys)
            ->concat($recentClients)
            ->concat($recentPracticeAreas)
            ->sortByDesc('created_at')
            ->take(10)
            ->values();

        return response()->json($activities);
    }

    /**
     * Get chart data for dashboard.
     */
    public function chartData(): JsonResponse
    {
        // Attorneys by state
        $attorneysByState = State::withCount('attorneys')
            ->having('attorneys_count', '>', 0)
            ->orderByDesc('attorneys_count')
            ->limit(10)
            ->get()
            ->map(function ($state) {
                return [
                    'name' => $state->name,
                    'count' => $state->attorneys_count
                ];
            });

        // Clients by attorney
        $clientsByAttorney = User::with('state')->where('user_type', 'ATTORNEY')->withCount('clients')
            ->having('clients_count', '>', 0)
            ->orderByDesc('clients_count')
            ->limit(10)
            ->get()
            ->map(function ($attorney) {
                return [
                    'name' => $attorney->full_name,
                    'count' => $attorney->clients_count
                ];
            });

        // Monthly registrations (last 12 months)
        $monthlyStats = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthlyStats[] = [
                'month' => $date->format('M Y'),
                'attorneys' => User::with('state')->where('user_type', 'ATTORNEY')->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'clients' => User::with('state')->where('user_type', 'CLIENT')->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
            ];
        }

        return response()->json([
            'attorneys_by_state' => $attorneysByState,
            'clients_by_attorney' => $clientsByAttorney,
            'monthly_stats' => $monthlyStats,
        ]);
    }

    /**
     * Get summary data for dashboard widgets.
     */
    public function summary(): JsonResponse
    {
        $summary = [
            'total_attorneys' => User::with('state')->where('user_type', 'ATTORNEY')->count(),
            'active_attorneys' => User::with('state')->where('user_type', 'ATTORNEY')->where('status', 'active')->count(),
            'total_clients' => User::with('state')->where('user_type', 'CLIENT')->count(),
            'active_clients' => User::with('state')->where('user_type', 'CLIENT')->where('status', 'active')->count(),
            'total_practice_areas' => PracticeArea::count(),
            'active_practice_areas' => PracticeArea::where('status', 'active')->count(),
            'total_states' => State::count(),
            'active_states' => State::where('status', 'active')->count(),
            'new_attorneys_this_month' => User::with('state')->where('user_type', 'ATTORNEY')->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'new_clients_this_month' => User::with('state')->where('user_type', 'CLIENT')->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];

        return response()->json($summary);
    }
}
