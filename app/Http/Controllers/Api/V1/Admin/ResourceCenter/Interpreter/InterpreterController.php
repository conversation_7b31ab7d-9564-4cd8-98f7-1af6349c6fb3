<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\Interpreter;

use App\Http\Controllers\Controller;
use App\Models\Interpreter;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class   InterpreterController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Interpreter::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('state', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($request->filled('availability')) {
            $query->where('availability', $request->get('availability'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('language')) {
            $query->whereJsonContains('languages', $request->get('language'));
        }

        if ($request->filled('specialization')) {
            $query->whereJsonContains('specializations', $request->get('specialization'));
        }

        if ($request->filled('city')) {
            $query->where('city', 'like', '%' . $request->get('city') . '%');
        }

        if ($request->filled('state')) {
            $query->where('state', 'like', '%' . $request->get('state') . '%');
        }

        if ($request->filled('min_rating')) {
            $query->where('rating', '>=', $request->get('min_rating'));
        }

        if ($request->filled('max_rate')) {
            $query->where('hourly_rate', '<=', $request->get('max_rate'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 25);
        $interpreters = $query->paginate($perPage);

        return response()->json([
            'data' => $interpreters->items(),
            'meta' => [
                'current_page' => $interpreters->currentPage(),
                'last_page' => $interpreters->lastPage(),
                'per_page' => $interpreters->perPage(),
                'total' => $interpreters->total(),
            ],
            'stats' => [
                'total' => Interpreter::count(),
                'available' => Interpreter::where('availability', 'available')->count(),
                'languages_covered' => collect(Interpreter::pluck('languages')->flatten())->unique()->count(),
                'average_rating' => Interpreter::where('total_jobs', '>', 0)->avg('rating'),
            ],
            'options' => [
                'availability' => Interpreter::getAvailabilityOptions(),
                'statuses' => Interpreter::getStatusOptions(),
                'languages' => Interpreter::getLanguageOptions(),
                'specializations' => Interpreter::getSpecializationOptions(),
                'certifications' => Interpreter::getCertificationOptions(),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:interpreters,email',
            'phone' => 'required|string|max:20',
            'emergency_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:50',
            'zip_code' => 'nullable|string|max:10',
            'languages' => 'required|array|min:1',
            'languages.*' => ['required', Rule::in(Interpreter::getLanguageOptions())],
            'specializations' => 'required|array|min:1',
            'specializations.*' => ['required', Rule::in(Interpreter::getSpecializationOptions())],
            'certifications' => 'nullable|array',
            'certifications.*' => ['required', Rule::in(Interpreter::getCertificationOptions())],
            'hourly_rate' => 'required|numeric|min:0|max:999.99',
            'experience_years' => 'required|integer|min:0|max:50',
            'bio' => 'nullable|string',
            'website' => 'nullable|url',
            'linkedin' => 'nullable|url',
            'service_areas' => 'nullable|array',
            'travel_available' => 'boolean',
            'travel_rate' => 'nullable|numeric|min:0|max:999.99',
        ]);

        $validated['joined_date'] = now()->toDateString();
        $validated['last_active'] = now();

        $interpreter = Interpreter::create($validated);

        return response()->json([
            'message' => 'Interpreter created successfully',
            'interpreter' => $interpreter
        ], 201);
    }

    public function show(Interpreter $interpreter): JsonResponse
    {
        return response()->json([
            'interpreter' => $interpreter
        ]);
    }

    public function update(Request $request, Interpreter $interpreter): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:interpreters,email,' . $interpreter->id,
            'phone' => 'sometimes|required|string|max:20',
            'emergency_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'sometimes|required|string|max:100',
            'state' => 'sometimes|required|string|max:50',
            'zip_code' => 'nullable|string|max:10',
            'languages' => 'sometimes|required|array|min:1',
            'languages.*' => ['required', Rule::in(Interpreter::getLanguageOptions())],
            'specializations' => 'sometimes|required|array|min:1',
            'specializations.*' => ['required', Rule::in(Interpreter::getSpecializationOptions())],
            'certifications' => 'nullable|array',
            'certifications.*' => ['required', Rule::in(Interpreter::getCertificationOptions())],
            'availability' => ['sometimes', 'required', Rule::in(Interpreter::getAvailabilityOptions())],
            'hourly_rate' => 'sometimes|required|numeric|min:0|max:999.99',
            'experience_years' => 'sometimes|required|integer|min:0|max:50',
            'status' => ['sometimes', 'required', Rule::in(Interpreter::getStatusOptions())],
            'bio' => 'nullable|string',
            'website' => 'nullable|url',
            'linkedin' => 'nullable|url',
            'service_areas' => 'nullable|array',
            'travel_available' => 'sometimes|boolean',
            'travel_rate' => 'nullable|numeric|min:0|max:999.99',
            'is_active' => 'sometimes|boolean',
        ]);

        $interpreter->update($validated);

        return response()->json([
            'message' => 'Interpreter updated successfully',
            'interpreter' => $interpreter->fresh()
        ]);
    }

    public function destroy(Interpreter $interpreter): JsonResponse
    {
        $interpreter->delete();

        return response()->json([
            'message' => 'Interpreter deleted successfully'
        ]);
    }

    public function updateAvailability(Request $request, Interpreter $interpreter): JsonResponse
    {
        $validated = $request->validate([
            'availability' => ['required', Rule::in(Interpreter::getAvailabilityOptions())]
        ]);

        if ($interpreter->updateAvailability($validated['availability'])) {
            return response()->json([
                'message' => 'Availability updated successfully',
                'interpreter' => $interpreter->fresh()
            ]);
        }

        return response()->json([
            'message' => 'Failed to update availability',
            'error' => 'Invalid availability status'
        ], 422);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate,update_status,update_availability',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:interpreters,id',
            'status' => ['required_if:action,update_status', Rule::in(Interpreter::getStatusOptions())],
            'availability' => ['required_if:action,update_availability', Rule::in(Interpreter::getAvailabilityOptions())],
        ]);

        $interpreters = Interpreter::whereIn('id', $validated['ids']);

        switch ($validated['action']) {
            case 'delete':
                $interpreters->delete();
                $message = 'Interpreters deleted successfully';
                break;

            case 'activate':
                $interpreters->update(['is_active' => true]);
                $message = 'Interpreters activated successfully';
                break;

            case 'deactivate':
                $interpreters->update(['is_active' => false]);
                $message = 'Interpreters deactivated successfully';
                break;

            case 'update_status':
                $interpreters->update(['status' => $validated['status']]);
                $message = 'Interpreters status updated successfully';
                break;

            case 'update_availability':
                $interpreters->update([
                    'availability' => $validated['availability'],
                    'last_active' => now()
                ]);
                $message = 'Interpreters availability updated successfully';
                break;
        }

        return response()->json(['message' => $message]);
    }
}
