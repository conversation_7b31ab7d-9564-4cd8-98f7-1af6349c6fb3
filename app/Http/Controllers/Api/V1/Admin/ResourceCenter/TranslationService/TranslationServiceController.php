<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\TranslationService;

use App\Http\Controllers\Controller;
use App\Models\TranslationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class TranslationServiceController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = TranslationService::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('company_name', 'like', "%{$search}%")
                  ->orWhere('contact_person', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('state', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('language')) {
            $query->whereJsonContains('languages', $request->get('language'));
        }

        if ($request->filled('specialization')) {
            $query->whereJsonContains('specializations', $request->get('specialization'));
        }

        if ($request->filled('city')) {
            $query->where('city', 'like', '%' . $request->get('city') . '%');
        }

        if ($request->filled('state')) {
            $query->where('state', 'like', '%' . $request->get('state') . '%');
        }

        if ($request->filled('min_rating')) {
            $query->where('rating', '>=', $request->get('min_rating'));
        }

        if ($request->filled('max_price_per_word')) {
            $query->where('price_per_word', '<=', $request->get('max_price_per_word'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'company_name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 25);
        $services = $query->paginate($perPage);

        return response()->json([
            'data' => $services->items(),
            'meta' => [
                'current_page' => $services->currentPage(),
                'last_page' => $services->lastPage(),
                'per_page' => $services->perPage(),
                'total' => $services->total(),
            ],
            'stats' => [
                'total' => TranslationService::count(),
                'active' => TranslationService::where('status', 'active')->count(),
                'languages_covered' => collect(TranslationService::pluck('languages')->flatten())->unique()->count(),
                'average_rating' => TranslationService::where('total_jobs', '>', 0)->avg('rating'),
            ],
            'options' => [
                'statuses' => ['active', 'inactive', 'pending'],
                'languages' => $this->getLanguageOptions(),
                'document_types' => $this->getDocumentTypeOptions(),
                'specializations' => $this->getSpecializationOptions(),
                'certifications' => $this->getCertificationOptions(),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'contact_person' => 'required|string|max:255',
            'email' => 'required|email|unique:translation_services,email',
            'phone' => 'required|string|max:20',
            'emergency_phone' => 'nullable|string|max:20',
            'website' => 'nullable|url',
            'address' => 'nullable|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:50',
            'zip_code' => 'nullable|string|max:10',
            'languages' => 'required|array|min:1',
            'languages.*' => 'required|string',
            'document_types' => 'required|array|min:1',
            'document_types.*' => 'required|string',
            'specializations' => 'required|array|min:1',
            'specializations.*' => 'required|string',
            'certifications' => 'nullable|array',
            'certifications.*' => 'required|string',
            'turnaround_time' => 'required|string|max:100',
            'price_per_word' => 'required|numeric|min:0|max:9.9999',
            'price_per_page' => 'required|numeric|min:0|max:999.99',
            'rush_fee_multiplier' => 'nullable|numeric|min:1|max:5',
            'description' => 'nullable|string',
            'service_areas' => 'nullable|array',
            'notarization_available' => 'boolean',
            'rush_service_available' => 'boolean',
            'minimum_order' => 'nullable|integer|min:0',
            'insurance_amount' => 'nullable|numeric|min:0',
            'license_number' => 'nullable|string|max:50',
        ]);

        $validated['joined_date'] = now()->toDateString();
        $validated['last_active'] = now();

        $service = TranslationService::create($validated);

        return response()->json([
            'message' => 'Translation service created successfully',
            'service' => $service
        ], 201);
    }

    public function show(TranslationService $translationService): JsonResponse
    {
        return response()->json([
            'service' => $translationService
        ]);
    }

    public function update(Request $request, TranslationService $translationService): JsonResponse
    {
        $validated = $request->validate([
            'company_name' => 'sometimes|required|string|max:255',
            'contact_person' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:translation_services,email,' . $translationService->id,
            'phone' => 'sometimes|required|string|max:20',
            'emergency_phone' => 'nullable|string|max:20',
            'website' => 'nullable|url',
            'address' => 'nullable|string',
            'city' => 'sometimes|required|string|max:100',
            'state' => 'sometimes|required|string|max:50',
            'zip_code' => 'nullable|string|max:10',
            'languages' => 'sometimes|required|array|min:1',
            'languages.*' => 'required|string',
            'document_types' => 'sometimes|required|array|min:1',
            'document_types.*' => 'required|string',
            'specializations' => 'sometimes|required|array|min:1',
            'specializations.*' => 'required|string',
            'certifications' => 'nullable|array',
            'certifications.*' => 'required|string',
            'turnaround_time' => 'sometimes|required|string|max:100',
            'price_per_word' => 'sometimes|required|numeric|min:0|max:9.9999',
            'price_per_page' => 'sometimes|required|numeric|min:0|max:999.99',
            'rush_fee_multiplier' => 'nullable|numeric|min:1|max:5',
            'status' => 'sometimes|required|in:active,inactive,pending',
            'description' => 'nullable|string',
            'service_areas' => 'nullable|array',
            'notarization_available' => 'sometimes|boolean',
            'rush_service_available' => 'sometimes|boolean',
            'minimum_order' => 'nullable|integer|min:0',
            'insurance_amount' => 'nullable|numeric|min:0',
            'license_number' => 'nullable|string|max:50',
            'is_active' => 'sometimes|boolean',
        ]);

        $translationService->update($validated);

        return response()->json([
            'message' => 'Translation service updated successfully',
            'service' => $translationService->fresh()
        ]);
    }

    public function destroy(TranslationService $translationService): JsonResponse
    {
        $translationService->delete();

        return response()->json([
            'message' => 'Translation service deleted successfully'
        ]);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate,update_status',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:translation_services,id',
            'status' => 'required_if:action,update_status|in:active,inactive,pending',
        ]);

        $services = TranslationService::whereIn('id', $validated['ids']);

        switch ($validated['action']) {
            case 'delete':
                $services->delete();
                $message = 'Translation services deleted successfully';
                break;

            case 'activate':
                $services->update(['is_active' => true]);
                $message = 'Translation services activated successfully';
                break;

            case 'deactivate':
                $services->update(['is_active' => false]);
                $message = 'Translation services deactivated successfully';
                break;

            case 'update_status':
                $services->update(['status' => $validated['status']]);
                $message = 'Translation services status updated successfully';
                break;
        }

        return response()->json(['message' => $message]);
    }

    private function getLanguageOptions(): array
    {
        return [
            'Spanish', 'French', 'German', 'Italian', 'Portuguese',
            'Mandarin', 'Cantonese', 'Japanese', 'Korean', 'Vietnamese',
            'Arabic', 'Hebrew', 'Persian', 'Turkish', 'Russian',
            'Polish', 'Dutch', 'Swedish', 'Norwegian', 'Danish',
            'Hindi', 'Urdu', 'Bengali', 'Tamil', 'Telugu'
        ];
    }

    private function getDocumentTypeOptions(): array
    {
        return [
            'Contracts', 'Court Documents', 'Depositions', 'Legal Briefs',
            'Immigration Papers', 'Business Documents', 'Patents',
            'Affidavits', 'Witness Statements', 'Legal Correspondence'
        ];
    }

    private function getSpecializationOptions(): array
    {
        return [
            'Legal', 'Medical', 'Technical', 'Business', 'Financial',
            'Immigration', 'Government', 'Academic', 'Literary'
        ];
    }

    private function getCertificationOptions(): array
    {
        return [
            'ATA Certified', 'ISO 17100', 'NAATI Certified',
            'Court Certified', 'Medical Translation Certified'
        ];
    }
}
