<?php

namespace App\Http\Controllers\Api\V1\Admin\ResourceCenter\ForumPost;

use App\Http\Controllers\Controller;
use App\Models\ForumPost;
use App\Models\ForumCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ForumPostController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = ForumPost::with(['category', 'user']);

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhereJsonContains('tags', $search);
            });
        }

        // Filters
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->get('category_id'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        if ($request->filled('is_pinned')) {
            $query->where('is_pinned', $request->boolean('is_pinned'));
        }

        if ($request->filled('is_locked')) {
            $query->where('is_locked', $request->boolean('is_locked'));
        }

        if ($request->filled('anonymous')) {
            $query->where('anonymous', $request->boolean('anonymous'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        // Special sorting for pinned posts
        if ($sortBy === 'created_at') {
            $query->orderBy('is_pinned', 'desc')->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = $request->get('per_page', 25);
        $posts = $query->paginate($perPage);

        return response()->json([
            'data' => $posts->items(),
            'meta' => [
                'current_page' => $posts->currentPage(),
                'last_page' => $posts->lastPage(),
                'per_page' => $posts->perPage(),
                'total' => $posts->total(),
            ],
            'stats' => [
                'total' => ForumPost::count(),
                'published' => ForumPost::where('status', 'published')->count(),
                'pending' => ForumPost::where('status', 'pending')->count(),
                'flagged' => ForumPost::where('status', 'flagged')->count(),
                'total_views' => ForumPost::sum('views'),
                'total_replies' => ForumPost::sum('replies'),
            ],
            'options' => [
                'statuses' => ['published', 'pending', 'flagged', 'archived'],
                'categories' => ForumCategory::where('is_active', true)->get(['id', 'name', 'color']),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:forum_categories,id',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'anonymous' => 'boolean',
            'attachments' => 'nullable|array',
        ]);

        // Generate slug from title
        $validated['slug'] = Str::slug($validated['title']);

        // Ensure unique slug
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (ForumPost::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Set user_id (assuming authenticated user)
        $validated['user_id'] = $request->user()->id ?? 1; // Fallback for testing

        // Set IP address
        $validated['ip_address'] = $request->ip();

        $post = ForumPost::create($validated);

        // Update category post count
        $post->category->increment('post_count');

        return response()->json([
            'message' => 'Forum post created successfully',
            'post' => $post->load(['category', 'user'])
        ], 201);
    }

    public function show(ForumPost $forumPost): JsonResponse
    {
        $forumPost->load(['category', 'user']);

        // Increment view count
        $forumPost->increment('views');

        return response()->json([
            'post' => $forumPost
        ]);
    }

    public function update(Request $request, ForumPost $forumPost): JsonResponse
    {
        $validated = $request->validate([
            'category_id' => 'sometimes|required|exists:forum_categories,id',
            'title' => 'sometimes|required|string|max:255',
            'content' => 'sometimes|required|string',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'status' => 'sometimes|required|in:published,pending,flagged,archived',
            'flag_reason' => 'nullable|string|max:255',
            'anonymous' => 'sometimes|boolean',
            'attachments' => 'nullable|array',
        ]);

        // Update slug if title changed
        if (isset($validated['title']) && $validated['title'] !== $forumPost->title) {
            $slug = Str::slug($validated['title']);

            // Ensure unique slug
            $originalSlug = $slug;
            $counter = 1;
            while (ForumPost::where('slug', $slug)->where('id', '!=', $forumPost->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $validated['slug'] = $slug;
        }

        // Set reviewed info if status is being changed
        if (isset($validated['status']) && $validated['status'] !== $forumPost->status) {
            $validated['reviewed_at'] = now();
            $validated['reviewed_by'] = $request->user()->id ?? null;
        }

        $forumPost->update($validated);

        return response()->json([
            'message' => 'Forum post updated successfully',
            'post' => $forumPost->fresh(['category', 'user'])
        ]);
    }

    public function destroy(ForumPost $forumPost): JsonResponse
    {
        // Update category post count
        $forumPost->category->decrement('post_count');

        $forumPost->delete();

        return response()->json([
            'message' => 'Forum post deleted successfully'
        ]);
    }

    public function pin(ForumPost $forumPost): JsonResponse
    {
        $forumPost->update(['is_pinned' => true]);

        return response()->json([
            'message' => 'Post pinned successfully',
            'post' => $forumPost->fresh()
        ]);
    }

    public function unpin(ForumPost $forumPost): JsonResponse
    {
        $forumPost->update(['is_pinned' => false]);

        return response()->json([
            'message' => 'Post unpinned successfully',
            'post' => $forumPost->fresh()
        ]);
    }

    public function lock(ForumPost $forumPost): JsonResponse
    {
        $forumPost->update(['is_locked' => true]);

        return response()->json([
            'message' => 'Post locked successfully',
            'post' => $forumPost->fresh()
        ]);
    }

    public function unlock(ForumPost $forumPost): JsonResponse
    {
        $forumPost->update(['is_locked' => false]);

        return response()->json([
            'message' => 'Post unlocked successfully',
            'post' => $forumPost->fresh()
        ]);
    }

    public function approve(ForumPost $forumPost): JsonResponse
    {
        $forumPost->update([
            'status' => 'published',
            'reviewed_at' => now(),
            'reviewed_by' => request()->user()->id ?? null,
            'flag_reason' => null
        ]);

        return response()->json([
            'message' => 'Post approved successfully',
            'post' => $forumPost->fresh()
        ]);
    }

    public function flag(Request $request, ForumPost $forumPost): JsonResponse
    {
        $validated = $request->validate([
            'reason' => 'required|string|max:255'
        ]);

        $forumPost->update([
            'status' => 'flagged',
            'flag_reason' => $validated['reason'],
            'reviewed_at' => now(),
            'reviewed_by' => $request->user()->id ?? null
        ]);

        return response()->json([
            'message' => 'Post flagged successfully',
            'post' => $forumPost->fresh()
        ]);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,approve,flag,archive,pin,unpin,lock,unlock',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:forum_posts,id',
            'reason' => 'required_if:action,flag|string|max:255',
        ]);

        $posts = ForumPost::whereIn('id', $validated['ids']);

        switch ($validated['action']) {
            case 'delete':
                // Update category post counts
                $posts->get()->each(function ($post) {
                    $post->category->decrement('post_count');
                });
                $posts->delete();
                $message = 'Posts deleted successfully';
                break;

            case 'approve':
                $posts->update([
                    'status' => 'published',
                    'reviewed_at' => now(),
                    'reviewed_by' => $request->user()->id ?? null,
                    'flag_reason' => null
                ]);
                $message = 'Posts approved successfully';
                break;

            case 'flag':
                $posts->update([
                    'status' => 'flagged',
                    'flag_reason' => $validated['reason'],
                    'reviewed_at' => now(),
                    'reviewed_by' => $request->user()->id ?? null
                ]);
                $message = 'Posts flagged successfully';
                break;

            case 'archive':
                $posts->update(['status' => 'archived']);
                $message = 'Posts archived successfully';
                break;

            case 'pin':
                $posts->update(['is_pinned' => true]);
                $message = 'Posts pinned successfully';
                break;

            case 'unpin':
                $posts->update(['is_pinned' => false]);
                $message = 'Posts unpinned successfully';
                break;

            case 'lock':
                $posts->update(['is_locked' => true]);
                $message = 'Posts locked successfully';
                break;

            case 'unlock':
                $posts->update(['is_locked' => false]);
                $message = 'Posts unlocked successfully';
                break;
        }

        return response()->json(['message' => $message]);
    }
}
