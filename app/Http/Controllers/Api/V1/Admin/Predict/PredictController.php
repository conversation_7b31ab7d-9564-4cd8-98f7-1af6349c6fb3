<?php

namespace App\Http\Controllers\Api\V1\Admin\Predict;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PredictController extends Controller
{

    public function index($input)
    {
        $url = 'http://3.144.211.131:8080/predict';
        $data = array('text' => $input);

        $options = array(
            CURLOPT_HTTPHEADER => array('Content-Type: application/json'),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
        );

        $ch = curl_init($url);
        curl_setopt_array($ch, $options);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            echo 'Curl error: ' . curl_error($ch);
        }

        curl_close($ch);

// $response now contains the response from the server
        echo json_decode($response);


    }


}
