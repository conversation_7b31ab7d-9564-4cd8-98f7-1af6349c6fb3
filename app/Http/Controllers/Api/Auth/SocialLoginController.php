<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Socialite\Facades\Socialite;

class SocialLoginController extends Controller
{

    /**
     * Redirect the user to the Provider authentication page.
     *
     * @param $provider
     * @return JsonResponse
     */
    public function redirectToProvider($provider)
    {
        $validated = $this->validateProvider($provider);
        if (!is_null($validated)) {
            return $validated;
        }

        return Socialite::driver($provider)->stateless()->redirect();
    }

    /**
     * Obtain the user information from Provider.
     *
     * @param $provider
     * @return JsonResponse
     */
    public function handleProviderCallback(Request $request): JsonResponse
    {

        $validated = $this->validateProvider($request->provider);
        if (!is_null($validated)) {
            return $validated;
        }
        $token = $request->access_token;
        $providerUser = Socialite::driver($request->provider)/*->userFromToken($token)*/;
        if($providerUser) {
            $userCreated = User::updateOrCreate(
                [
                    'email' => $request->email,

                ],
                [
                    'email_verified_at' => now(),
                    'name' => $request->name,
                    'status' => true,
                    'user_type' => getUserType(),
                    'phone' => emailOrPhone($request->email) == "phone" ? $request->email : null,
                    'avatar' => $request->avatar ?? "https://ui-avatars.com/api/?background=ca313b&color=fff&rounded=true&bold=true&name=" . $request->name

                ]
            );
            $userCreated->new_user = $userCreated->wasRecentlyCreated;
            $userCreated->providers()->updateOrCreate(
                [
                    'provider' => $request->provider,
                    'provider_id' => $request->provider_id,
                ]
            );

            Auth::login($userCreated);
            $token = auth()->user()->createToken(env('ACCESS_TOKEN'))->accessToken;

            return successResponse(['user' => $userCreated, 'token' => $token]);
        }else{
            return failedResponse(trans('response.errorLogin'));
        }
    }

    /**
     * @param $provider
     * @return JsonResponse
     */
    protected function validateProvider($provider)
    {
        if (!in_array($provider, ['facebook', 'linkedin', 'google','apple'])) {
            return response()->json(['error' => trans('response.socialLoginError')], 422);
        }
    }
}
