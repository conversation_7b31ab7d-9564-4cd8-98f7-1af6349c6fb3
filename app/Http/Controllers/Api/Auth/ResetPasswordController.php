<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\ResetPasswordRequest;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class ResetPasswordController extends Controller
{
    protected function sendResetResponse(ResetPasswordRequest $request): \Illuminate\Http\JsonResponse
    {
        try {
            $input = $request->all();
            if (!DB::table('password_resets')->where('token', $request->token)->first()) {
                return failedResponse(trans('response.invalidToken'));
            }
            if (!$user = User::where('email', $request->email)->first()) {
                return failedResponse(trans('response.userNotExist'));
            }

            $user->password = Hash::make($request->password);
            $user->save();
            DB::table('password_resets')->where('token', $request->token)->delete();
            return successResponse(trans('response.passwordResetSuccess'));
        } catch (\Exception $exception) {
            return failedResponse(trans('response.emailCouldNotSend'));

        }
    }

}
