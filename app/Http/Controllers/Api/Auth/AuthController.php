<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\ChangePasswordRequest;
use App\Http\Requests\RegisterRequest;
use App\Http\Requests\LoginRequest;
use App\Jobs\ResetPassword;
use App\Jobs\ResetPasswordViaSms;
use App\Jobs\VerifyAccount;
use App\Jobs\VerifyAccountViaSms;
use App\Models\Code;
use App\Models\User;
use App\Traits\ProfilePoints\ProfilePoints;
use Carbon\Carbon;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Auth\Passwords\PasswordBroker;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Twilio\Rest\Client;

class AuthController extends Controller
{

    use ProfilePoints;

    public function register(RegisterRequest $request): JsonResponse
    {

        try {

            DB::beginTransaction();
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => emailOrPhone($request->email) == "phone" ? $request->email : null,
                'phone_code' => $request->phone_code,
                'password' => bcrypt($request->password),
                'user_type' => getUserType(),
                'avatar' => "https://ui-avatars.com/api/?background=ca313b&color=fff&rounded=true&bold=true&name=" . $request->name,
            ]);
            $checkPhone = $this->sendAccountVerifyCode($request);
            $token = $user->createToken(env('ACCESS_TOKEN'))->accessToken;
            session(['user-token' => $token]);
            DB::commit();
            return successResponse(['user' => $user, 'token' => $token]);
        } catch (\Exception $exception) {
            DB::rollBack();
            return failedResponse(trans('response.failedRegistration'));
        }
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $emailOrPhone = emailOrPhone($request->email);
        if($emailOrPhone) {
            $data = [$emailOrPhone => $request->get('email'), 'password' => $request->get('password'),
                'user_type' => getUserType()];
            if (auth()->attempt($data)) {
                $token = auth()->user()->createToken(env('ACCESS_TOKEN'))->accessToken;
                if (auth()->user()->deactivated_at
                    && auth()->user()->deletion_scheduled_at
                    && Carbon::now()->lessThanOrEqualTo(auth()->user()->deletion_scheduled_at)) {
                    auth()->user()->deactivated_at = null;
                    auth()->user()->deletion_scheduled_at = null;
                    auth()->user()->save();
                }

                return successResponse(['user' => auth()->user(), 'token' => $token], 200);
            } else {
                return failedResponse(trans('response.invalidCredential'));

            }
        }else{
            return failedResponse(trans('response.invalidCredentialData'));
        }
    }

    public function logout(Request $request): JsonResponse
    {
        if (Auth::check()) {
            $user = Auth::user();
            $user->fcm_token = null;
            $user->save();
            $token = $user->token();
            $token->revoke();
            return successResponse(trans('response.logOut'));
        } else {
            return failedResponse(trans('response.unauthorised'));
        }
    }


    public function verify(Request $request): JsonResponse
    {
        $verifcation = Code::where('code', $request->code)->where('type', $request->type)->first();
        if (!$verifcation)
            return failedResponse(trans('response.invalidCode'));
        $passwordResetToken = null;
        if ($request->code == $verifcation->code) {
            $user = User::where('id', $verifcation->user_id);
            if ($request->type == 2) { //1 for reset password 2 for verify account
                $user->update(['status' => 1, 'email_verified_at' => Carbon::now()]);
                $this->addProfilePoints();
            } else {
                $passwordResetToken = DB::table('password_resets')
                    ->where('email', $user->first()->email)->select('token as password_reset_token')->first();
            }
            return successDataResponse(['profile_points'=> Auth::user()->profile_points ?? session('profile_points') ,'passwordResetToken' => $passwordResetToken], 'Verified successfully');
        } else {
            return failedResponse(trans('response.unableToVerify'));
        }
    }

    public function sendAccountVerifyCode(Request $request): JsonResponse
    {
        try {
            $checkEmailOrPhone = emailOrPhone($request->email);
            if($checkEmailOrPhone) {
                $code = generateCode();

                $user = User::where($checkEmailOrPhone, $request->email)->first();
                if ($user) {
                    Code::updateOrCreate(
                        ['user_id' => $user->id],
                        ['code' => $code, 'type' => '2']
                    );
                    if ($checkEmailOrPhone == 'email') {
                        VerifyAccount::dispatch($user, $code);
                    } else {
                        VerifyAccountViaSms::dispatch($user, $code);
                    }
                } else {
                    return failedResponse(trans('response.notFoundInDataset'));
                }
                return successResponse(trans('response.verificationCodeSent'));
            }else{
                return failedResponse(trans('response.invalidCredentialData'));
            }
        } catch (\Exception $exception) {
            return failedResponse($exception->getMessage());
        }
    }

    public
    function change_password(ChangePasswordRequest $request): JsonResponse
    {
        $input = $request->all();
        $userid = Auth::user()->id;
        try {
            if ((Hash::check(request('old_password'), Auth::user()->password)) == false) {
                return failedResponse(trans('response.checkOldPassword'));
            } else if ((Hash::check(request('new_password'), Auth::user()->password)) == true) {
                return failedResponse(trans('response.enterNonSimilarPassword'));
            } else {
                User::where('id', $userid)->update(['password' => Hash::make($input['new_password'])]);
                return successResponse(trans('response.passwordUpdated'));
            }
        } catch (\Exception $ex) {
            if (isset($ex->errorInfo[2])) {
                $msg = $ex->errorInfo[2];
            } else {
                $msg = $ex->getMessage();
            }
            return failedResponse($msg);
        }

    }

    public function updateToken(Request $request): JsonResponse
    {
        try{
            Log::debug($request->all());
            $request->user()->update(['fcm_token'=>$request->token,'deviceToken' => $request->deviceToken]);
            return successResponse(trans('response.fcmTokenUpdated'));
        }catch(\Exception $e){
            Log::error($e);
            return failedResponse($e);
        }
    }

    public function deactivate(): JsonResponse
    {
        $user = Auth::user();

        if (!$user) {
            return failedResponse('User not found',404);
        }
//        $user->deactivated_at = Carbon::now();
//        $user->deletion_scheduled_at = Carbon::now()->addDays(7);
//        $user->save();
        $user->delete();
        return successResponse('User deactivated and scheduled for deletion in 7 days', 200);
    }
}

