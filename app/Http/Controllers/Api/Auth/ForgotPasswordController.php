<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\ResetPasswordRequest;
use App\Jobs\ResetPassword;
use App\Jobs\ResetPasswordViaSms;
use App\Models\Code;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class ForgotPasswordController extends Controller
{
    protected function sendResetLinkResponse(ForgotPasswordRequest $request): \Illuminate\Http\JsonResponse
    {
        try {
            $email = $request->email;
            $checkEmailOrPhone = emailOrPhone($email);
            if($checkEmailOrPhone) {
                $code = generateCode();
                $user = User::where($checkEmailOrPhone, $email)->first();
                if ($user) {
                    Code::updateOrCreate(
                        ['user_id' => $user->id],
                        ['code' => $code, 'type' => '1']
                    );
                    $token = bcrypt(Str::random(40));
                    DB::table('password_resets')->updateOrInsert(
                        ['email' => $request->email],
                        ['token' => $token, 'created_at' => Carbon::now()]
                    );
                    if ($checkEmailOrPhone == 'email') {
                        ResetPassword::dispatch($user, $code);
                    } else {
                        ResetPasswordViaSms::dispatch($user, $code);
                    }
                } else {
                    return failedResponse(trans('response.notFoundInDataset'));
                }
                return successResponse(trans('response.passwordResetCodeSent'));
            }else{
                return failedResponse(trans('response.invalidCredentialData'));
            }
        } catch (\Exception $exception) {
            return failedResponse($exception->getMessage());
        }
    }

}
