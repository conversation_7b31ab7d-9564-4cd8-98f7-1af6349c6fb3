<?php

use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use Illuminate\Http\Response;

function successResponse($message = 'success', $code = 200)
{
    return response()->json(new SuccessResource([
        'message' => $message,
        'status_code' => $code
    ]), $code);
}

function successDataResponse($data = [], string $message = '', int $code = 200)
{
    $data = ['status_code' => $code, 'data' => $data];
    if ($message) $data['message'] = $message;

    return response()->json(new SuccessResource($data), $code);
}

function successPaginationResponse($data = [],  string $message = '', int $code = 200)
{
    if ($message) $data['message'] = $message;
    return response()->json(new SuccessResource($data), $code);
}

function failedResponse($message = 'Failed getting data.', int $code = 400)
{
    if ($message instanceof \Throwable) {
        $status_code = $message->getCode();
        $message = $message->getMessage();
        $code = in_array($status_code, array_keys(Response::$statusTexts)) ? $status_code : 400;
    }
    return response()->json(new ErrorResource([
        'message' => $message,
        'status_code' => $code
    ]),
        $code);
}

function failedResponseData(string $message = 'Failed getting data.', int $code = 400, $data)
{
    if ($message instanceof \Throwable) {
        $status_code = $message->getCode();
        $message = $message->getMessage();
        $code = in_array($status_code, array_keys(Response::$statusTexts)) ? $status_code : 400;
    }
    return response()->json(new ErrorResource([
            'message' => $message,
            'status_code' => $code
        ] + $data),
        $code);
}
