<?php

// function to check either the input is phone number or email address

use App\Utils\RtcTokenBuilder2;
use <PERSON><PERSON><PERSON>\Rest\Client;
use Illuminate\Http\Request;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Auth;
use Carbon\CarbonTimeZone;
use Illuminate\Support\Facades\Log;
use Google\Auth\Credentials\ServiceAccountCredentials;
use Google\Auth\Middleware\AuthTokenMiddleware;
use GuzzleHttp\HandlerStack;
use Google\Client as GoogleClient;

if (!function_exists('emailOrPhone')) {
    function emailOrPhone($data): string
    {
        $input = $data;
// Use Laravel's built-in validator to check if the input is an email
        if (preg_match("/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$/", $input)) {
            return "email";
        } // Use a regular expression to check if the input is a phone number
        else if (preg_match("/^([0|\+[0-9]{1,1})?([0-9][0-9]{9})$/", $input)) {
            return "phone";
        } else {
            return false;
        }
    }


}

if (!function_exists('sendSms')) {
    function sendSms($phone, $message)
    {
        $accountSid = env('TWILIO_ACCOUNT_SID', '**********************************');
        $authToken = env('TWILIO_AUTH_TOKEN', '630c8d6bce928f9a4b6b8d88339ba886');
        try {
            $client = new Client($accountSid, $authToken);
            $result = $client->messages->create
            ($phone,
                [
                    'from' => env('TWILIO_NUMBER', '**********'),
                    'body' => $message
                ]
            );

            return $result;
        } catch (\Exception $e) {
            echo "Error: " . $e->getMessage();
        }
    }

    if (!function_exists('generateCode')) {
        function generateCode()
        {
            $code = rand(100000, 999999); //generate random code
            return $code;
        }
    }

}

function sanitizeFileName($file, bool $makeUnique = false)
{
    $imgName = $file->getClientOriginalName();
    $indexOFF = strrpos($imgName, '.');
    $nameFile = substr($imgName, 0, $indexOFF);
    $extension = substr($imgName, $indexOFF);
    $clean = preg_replace("([^\w\s\d\-_~,;\[\]\(\)])", "", $nameFile);
    $fileName = substr(str_replace(' ', '', $clean) . '_' . uniqid() . $extension, -100);
    return $makeUnique ? uniqid() . $fileName : $fileName;
}

function fcmToken($userId)
{
    $userFcm = \App\Models\User::where('id', $userId)->first();
    return $userFcm->fcm_token;
}


//function sendNotification($title,
//                          $body,
//                          $FcmToken,
//                          $messageObject = null,
//                          $type = null,
//                          $receiver = null,
//                          $sender = null,
//                          $callId = null,
//                          $isVideo = false,
//                          $device_id = null,
//                          $apnCallId = null,
//                          $callLog = null,
//                          $env=null)
//{
//    $url = 'https://fcm.googleapis.com/fcm/send';
//    $data["data"] = [
//        "type" => "contract",
//        "action" => "accepted"
//    ];
//    switch ($type) {
//        case 1:
//            $typeData = [
//                "type" => "chat",
//                "message" => $messageObject ?? null
//            ];
//            break;
//        case 2:
//            $typeData = [
//                "type" => "contract",
//                "action" => "accepted"
//            ];
//            break;
//        case 3:
//            $typeData = [
//                "type" => "contract",
//                "action" => "requested"
//            ];
//            break;
//        case 4:
//            $typeData = [
//                "type" => "appointment",
//                "action" => "created",
//                "sender" => Auth::user(),
//                "receiver" => \App\Models\User::where('id', $receiver)->first(),
//
//            ];
//            break;
//        case 5:
//            $typeData = [
//                "type" => "appointment",
//                "action" => "accepted",
//                "sender" => Auth::user(),
//                "receiver" => \App\Models\User::where('id', $receiver)->first(),
//
//            ];
//            break;
//        case 6:
//            $typeData = [
//                "type" => "appointment",
//                "action" => "cancelled",
//                "sender" => Auth::user(),
//                "receiver" => \App\Models\User::where('id', $receiver)->first(),
//            ];
//            break;
//        case 7:
//            $typeData = [
//                "type" => "appointment",
//                "action" => "scheduled",
//                "sender" => \App\Models\User::where('id', $sender)->first(),
//                "receiver" => \App\Models\User::where('id', $receiver)->first(),
//
//            ];
//            break;
//        case 8:
//            $sender = \App\Models\User::where('id', Auth::user()->id)->first();
//            $to_user_id = \App\Models\User::where('id', $receiver)->first();
//            $typeData = [
//                "type" => "call",
//                "action" => "created",
//                "sender" => $sender,
//                "to_user_id" => $to_user_id,
//                "callId" => $callId,
//                "video" => $isVideo,
//                "apnCallId" => $apnCallId,
//                "deviceId" => $device_id ? initCallIos($device_id, $sender, $isVideo, $apnCallId, $callLog, $env) : null,
//                "callLog" => $callLog
//            ];
//            if (isset($device_id) && $device_id != null) {
//                return true;
//            }
//            break;
//        case 9:
//            $typeData = [
//                "type" => "callEnded",
//                "action" => "ended",
//                "sender" => \App\Models\User::where('id', Auth::user()->id)->first(),
//                "to_user_id" => \App\Models\User::where('id', $receiver)->first(),
//            ];
//            break;
//    }
//
//    $data = [
//        "to" => $FcmToken,
//        "notification" => [
//            "title" => $title ?? Auth::user()->name,
//            "body" => $body,
//            "priority" => "high",
//        ],
//        "data" => $typeData,
//    ];
//
//    $encodedData = json_encode($data);
//    $headers = [
//        'Authorization:key='.env('FIREBASE_SERVER_KEY'),
//        'Content-Type: application/json',
//    ];
//
//    $ch = curl_init();
//
//    curl_setopt($ch, CURLOPT_URL, $url);
//    curl_setopt($ch, CURLOPT_POST, true);
//    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
//    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
//    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
//    // Disabling SSL Certificate support temporarly
//    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
//    curl_setopt($ch, CURLOPT_POSTFIELDS, $encodedData);
//    // Execute post
//    $result = curl_exec($ch);
//    dd($result);
//    if ($result === FALSE) {
//        die('Curl failed: ' . curl_error($ch));
//    }
//    // Close connection
//    curl_close($ch);
//    // FCM response
//    return $result;
//}

//function sendNotification(
//    $title,
//    $body,
//    $FcmToken,
//    $messageObject = null,
//    $type = null,
//    $receiver = null,
//    $sender = null,
//    $callId = null,
//    $isVideo = false,
//    $device_id = null,
//    $apnCallId = null,
//    $callLog = null,
//    $env = null
//) {
//    try {
//        Log::info("Notification of type $type from " . (Auth::id() ?? 'System') . " to " . ($receiver ?? 'Unknown'));
//
//        $client = new GoogleClient();
//        $serviceAccountPath = storage_path(env('FIREBASE_SERVICE_ACCOUNT_PATH', 'app/google-services.json'));
//
//        if (!file_exists($serviceAccountPath)) {
//            throw new Exception("Firebase service account file not found at: $serviceAccountPath");
//        }
//
//        $client->setAuthConfig($serviceAccountPath);
//        $client->addScope('https://www.googleapis.com/auth/firebase.messaging');
//
//        $accessTokenData = $client->fetchAccessTokenWithAssertion();
//        if (!isset($accessTokenData["access_token"])) {
//            throw new Exception("Failed to fetch Firebase access token. Response: " . json_encode($accessTokenData));
//        }
//        $accessToken = $accessTokenData["access_token"];
//
//        $projectId = env('FIREBASE_PROJECT_ID', 'legal-fiber');
//        $url = "https://fcm.googleapis.com/v1/projects/$projectId/messages:send";
//
//        switch ($type) {
//            case 1:
//                $typeData = ["type" => "chat", "message" => $messageObject];
//                break;
//            case 2:
//                $typeData = ["type" => "contract", "action" => "accepted"];
//                break;
//            case 3:
//                $typeData = ["type" => "contract", "action" => "requested"];
//                break;
//            case 4:
//            case 5:
//            case 6:
//                $action = $type === 4 ? "created" : ($type === 5 ? "accepted" : "cancelled");
//                $typeData = [
//                    "type" => "appointment",
//                    "action" => $action,
//                    "sender" => Auth::user(),
//                    "receiver" => \App\Models\User::find($receiver),
//                ];
//                break;
//            case 7:
//                $typeData = [
//                    "type" => "appointment",
//                    "action" => "scheduled",
//                    "sender" => \App\Models\User::find($sender),
//                    "receiver" => \App\Models\User::find($receiver),
//                ];
//                break;
//            case 8:
//                $senderUser = \App\Models\User::find(Auth::id());
//                $receiverUser = \App\Models\User::find($receiver);
//                $typeData = [
//                    "type" => "call",
//                    "action" => "created",
//                    "sender" => $senderUser,
//                    "to_user_id" => $receiverUser,
//                    "callId" => $callId,
//                    "video" => $isVideo,
//                    "apnCallId" => $apnCallId,
//                    "deviceId" => $device_id ? initCallIos($device_id, $senderUser, $isVideo, $apnCallId, $callLog, $env) : null,
//                    "callLog" => $callLog,
//                ];
//                if ($device_id) {
//                    return true; // Skip FCM if device ID is handled by initCallIos
//                }
//                break;
//            case 9:
//                $typeData = [
//                    "type" => "callEnded",
//                    "action" => "ended",
//                    "sender" => Auth::user(),
//                    "to_user_id" => \App\Models\User::find($receiver),
//                ];
//                break;
//            default:
//                throw new Exception("Unsupported notification type: $type");
//        }
//        $data = [
//            'message' => [
//                'token' => $FcmToken,
//                'notification' => [
//                    'title' => $title,
//                    'body' => $body,
//                ],
//                'data' => $typeData,
//            ],
//        ];
//
//        $encodedData = json_encode($data);
//
//        $headers = [
//            "Authorization: Bearer $accessToken",
//            "Content-Type: application/json",
//        ];
//
//        $ch = curl_init();
//        curl_setopt($ch, CURLOPT_URL, $url);
//        curl_setopt($ch, CURLOPT_POST, true);
//        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
//        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
//        curl_setopt($ch, CURLOPT_POSTFIELDS, $encodedData);
//
//        $result = curl_exec($ch);
//        if ($result === false) {
//            $curlError = curl_error($ch);
//            curl_close($ch);
//            throw new Exception('cURL Error: ' . $curlError);
//        }
//
//        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
//        curl_close($ch);
//
//        $decodedResult = json_decode($result, true);
//
//        if ($httpCode != 200) { // Check for HTTP errors (e.g., 400, 401, 500)
//            Log::error("FCM HTTP Error: " . $httpCode . ", Response: " . $result);
//            if (isset($decodedResult['error']['details'])) {
//                foreach ($decodedResult['error']['details'] as $detail) {
//                    if (isset($detail['fieldViolations'])) {
//                        foreach ($detail['fieldViolations'] as $violation) {
//                            if ($violation['field'] === 'message.token') {
//                                throw new Exception("Invalid registration token: " . $violation['description']);
//                            }
//                        }
//                    }
//                }
//            }
//            dd($result);
//            throw new Exception("FCM API Error: HTTP " . $httpCode . ", Response: " . $result);
//        }
//        Log::info("FCM Response: $result");
//        return $decodedResult;
//
//    } catch (Exception $e) {
//        dd($e);
//        Log::error("Notification Error: " . $e->getMessage());
//        return ['success' => false, 'error' => $e->getMessage()];
//    }
//}

function sendNotification(
    $title,
    $body,
    $FcmToken,
    $messageObject = null,
    $type = null,
    $receiver = null,
    $sender = null,
    $callId = null,
    $isVideo = false,
    $device_id = null,
    $apnCallId = null,
    $callLog = null,
    $env = null
)
{
    Log::info("Sending notification to $type");
            $useNotificationOnly = false;
            switch ($type) {
            case 1:
                $title = Auth::user()->name;
                $typeData = ["type" => "chat", "message" => "$messageObject"];
                break;
            case 2:
                $typeData = ["type" => "contract", "action" => "accepted"];
                break;
            case 3:
                $typeData = ["type" => "contract", "action" => "requested"];
                break;
            case 4:
            case 5:
            case 6:
                $action = $type === 4 ? "created" : ($type === 5 ? "accepted" : "cancelled");
                $typeData = [
                    "type" => "appointment",
                    "action" => $action,
                    "sender" => Auth::user(),
                    "receiver" => \App\Models\User::find($receiver),
                ];
                break;
            case 7:
                $typeData = [
                    "type" => "appointment",
                    "action" => "scheduled",
                    "sender" => \App\Models\User::find($sender),
                    "receiver" => \App\Models\User::find($receiver),
                ];
                break;
            case 8:
                $useNotificationOnly = true;
                $senderUser = Auth::user();
                $receiverUser = \App\Models\User::find($receiver);
                $typeData = [
                    "type" => "call",
                    "action" => "created",
                    "sender" => $senderUser,
                    "to_user_id" => $receiverUser,
                    "callId" => $callId,
                    "video" => $isVideo,
                    "apnCallId" => $apnCallId,
                    "deviceId" => $device_id ? initCallIos($device_id, $senderUser, $isVideo, $apnCallId, $callLog, $env,false) : null,
                    "callLog" => $callLog,
                ];
                Log::info('Device_ID:'.$device_id);
                if ($device_id) {
                    return true; // Skip FCM if device ID is handled by initCallIos
                }else{
                    $useNotificationOnly = false;
                }
                break;
            case 9:
                $useNotificationOnly = true;
                $typeData = [
                    "type" => "callEnded",
                    "action" => "ended",
                    "sender" => Auth::user(),
                    "to_user_id" => \App\Models\User::find($receiver),
                ];
                break;
            default:
                throw new Exception("Unsupported notification type: $type");
        }

    $notification = new \App\Services\FirebaseNotification();
    return $notification->sendNotification($FcmToken,$title,$body,$typeData,$useNotificationOnly);
}

function getTimeZones()
{
    $timeZones = collect(CarbonTimeZone::listIdentifiers())
        ->map(function ($tz) {
            $carbonTz = new CarbonTimeZone($tz);
            return [
                'name' => $carbonTz->getName(),
                'utc' => $carbonTz->toOffsetName(),
            ];
        });

    return response()->json($timeZones);
}

function rtcToken($uids, $chanelName)
{
    $appId = "612cfa9be2eb4e4eae6d90415f573f5d";
    $appCertificate = "b244ff79dd46487398b17a0f8fb43a92";
    $channelName = $chanelName;
    $uid = $uids;
    $tokenExpirationInSeconds = 3600;
    $privilegeExpirationInSeconds = 3600;

    return RtcTokenBuilder2::buildTokenWithUid(
        $appId,
        $appCertificate,
        $channelName,
        $uid,
        RtcTokenBuilder2::ROLE_PUBLISHER,
        $tokenExpirationInSeconds,
        $privilegeExpirationInSeconds
    );
}

//function initCallIos($deviceToken, $user,$isVideo, $apnCallId,$callLog,$env)
//{
//    $data = array(
//        'aps' => array(
//            'content-available' => 1,
//            'alert' => array(
//                'id' => $apnCallId,
//                'type' => $isVideo,
//                'nameCaller' => $user->name,
//                'extra' => array(
//                    'callLog' => $callLog,
//                )
//            )
//        )
//    );
//
//    $jsonData = json_encode($data);
//   if(env('APP_ENV') == 'local'){
//        $url = "https://api.sandbox.push.apple.com/3/device/$deviceToken";
//    }else {
//        $url = "https://api.push.apple.com/3/device/$deviceToken";
//    }
//
//
//    \Illuminate\Support\Facades\Log::info("URL: ". $url);
//// cURL initialization
//    $ch = curl_init($url);
//
//// Set cURL options
//    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//    curl_setopt($ch, CURLOPT_POST, true);
//    curl_setopt($ch, 	CURLOPT_SSL_VERIFYPEER, false);
//
//    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
//    curl_setopt($ch, CURLOPT_HTTPHEADER, [
//        'apns-push-type: voip',
//        'apns-expiration: 0',
//        'apns-priority: 0',
//        'apns-topic: com.legalfiber.app.voip',
//    ]);
//    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_2);
//    curl_setopt($ch, CURLOPT_SSLCERT, public_path( 'voip_certificate.pem'));
//    curl_setopt($ch, CURLOPT_SSLCERTPASSWD, 'legalfiber@2599');
//
//// Execute the cURL request
//    $response = curl_exec($ch);
//    $httpStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
//    \Illuminate\Support\Facades\Log::info('HTTP Status Code: ' . $httpStatus);
//    \Illuminate\Support\Facades\Log::info('Response: '.$response);
//    if ($response === false) {
//        \Illuminate\Support\Facades\Log::info('cURL error: ' . curl_error($ch));
//    } else {
//        \Illuminate\Support\Facades\Log::info('Notification sent successfully!');
//    }
//// Close cURL handle
//    curl_close($ch);
//}


function initCallIos($deviceToken, $user, $isVideo, $apnCallId, $callLog, $env,$callEnded)
{
    Log::info('initCallIos called with: '. json_encode(func_get_args()));

    // Correct payload structure for VoIP notifications
//    $payload = array(
//        'aps' => array(
//            'content-available' => 1, // Required for VoIP
//            'priority' => 10,        // Must be 10 for VoIP
//            'alert' => array(        // Optional, but can provide a fallback alert
//                'title' => 'Incoming Call',
//                'body' => $user->name . ' is calling...',
//            ),
//            'sound' => 'default', //Optional, but can add default ringtone
//        ),
//        'id' => $apnCallId,
//        'type' => $isVideo ? 'video' : 'audio', // Use string values
//        'nameCaller' => $user->name,
//        'extra' => array(
//            'callLog' => $callLog,
//        )
//    );

    $payload = array(
        'aps' => array(
            'alert' => array(
                'id' => $apnCallId,
                'type' => $isVideo,
                'nameCaller' => $user->name,
                'callEnded' => $callEnded,
                'extra' => array(
                    'callLog' => $callLog,
                )
            )
        )
    );


    $jsonData = json_encode($payload);
    Log::info("APNs Payload: ".$jsonData);

    // Environment check
    if(env('APP_ENV') == 'local') {
        $url = "https://api.sandbox.push.apple.com/3/device/$deviceToken";
    } else {
        $url = "https://api.push.apple.com/3/device/$deviceToken";
    }

    \Illuminate\Support\Facades\Log::info("APNs URL: ". $url);

    // cURL initialization
    $ch = curl_init($url);

    // Set cURL options
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        //CURLOPT_SSL_VERIFYPEER => false, // Consider enabling in production with valid certs.  *VERY IMPORTANT*
        CURLOPT_POSTFIELDS => $jsonData,
        CURLOPT_HTTPHEADER => [
            'apns-push-type: voip',
            'apns-expiration: 0',
            'apns-priority: 10', // Critical for VoIP immediate delivery
            'apns-topic: com.legalfiber.app.voip', // ***VERY IMPORTANT***
            'Content-Type: application/json'
        ],
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2,
        CURLOPT_SSLCERT => public_path('voip_certificate.pem'),
        CURLOPT_SSLCERTPASSWD => 'legalfiber@2599'
    ]);

    // Execute request
    $response = curl_exec($ch);
    $httpStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    \Illuminate\Support\Facades\Log::info('APNs HTTP Status Code: ' . $httpStatus);
    \Illuminate\Support\Facades\Log::info('APNs Response: '.$response);

    if ($response === false) {
        $error = 'cURL error: ' . curl_error($ch);
        \Illuminate\Support\Facades\Log::error($error);
        curl_close($ch);
        return $error; // Return the error message
    } else {
        \Illuminate\Support\Facades\Log::info('APNs Notification sent successfully!');
    }

    curl_close($ch);

    // Check for common APNs errors
    if ($httpStatus != 200) {
        \Illuminate\Support\Facades\Log::error("APNs returned HTTP status code: " . $httpStatus);
        return "APNs error: HTTP status " . $httpStatus . ", response: " . $response;
    }

    return true; // Indicate success
}





function getFirebaseAccessToken()
{
    $credentialsFile = env('FIREBASE_SERVER_KEY');
    $scopes = ['https://www.googleapis.com/auth/cloud-platform'];

    $credentials = new ServiceAccountCredentials($scopes, $credentialsFile);
    $middleware = new AuthTokenMiddleware($credentials);
    $stack = HandlerStack::create();
    $stack->push($middleware);

    $client = new Client(['handler' => $stack]);
    $response = $client->get('https://www.googleapis.com/oauth2/v1/tokeninfo');
    $data = json_decode($response->getBody()->getContents(), true);

    return $data['access_token'];
}

