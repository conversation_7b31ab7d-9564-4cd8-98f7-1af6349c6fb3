<?php

namespace App\Console\Commands;

use App\Models\Appointment;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use function Symfony\Component\Translation\t;

class AppointmentReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'appointment:reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $processedAppointments = Cache::get('processed_appointments') ?? [];
        $currentServerTime = Carbon::now();
        $appointments = Appointment::where('time','>',$currentServerTime->subDay())->get();
        if (!$appointments->isEmpty()) {
            foreach ($appointments as $appointment) {
                $currentTimeInAppointmentTZ = Carbon::now()->setTimezone($appointment->utc);
                $startTime = $currentTimeInAppointmentTZ->copy()->addMinutes(15);
                $notificationTime = Carbon::createFromFormat('Y-m-d H:i P', $appointment->time.' '. $appointment->utc);
                if ($notificationTime->between($currentTimeInAppointmentTZ, $startTime, true)) {
                    if (!in_array($appointment->id, $processedAppointments)) {
                        // Send notifications here
                        sendNotification("LegalFiber", "You have an appointment with {$appointment->attorney->name} scheduled within 15 minutes!", fcmToken($appointment->client_id), null, 7, $appointment->client_id,$appointment->attorney_id);
                        sendNotification("LegalFiber", "You have an appointment with {$appointment->client->name} scheduled within 15 minutes!", fcmToken($appointment->attorney_id), null, 7, $appointment->attorney_id,$appointment->client_id);

                        $processedAppointments[] = $appointment->id;
                        Cache::put('processed_appointments', $processedAppointments); // Cache for 60 minutes (adjust as needed)
                    }
                }
            }

        }
    }
}
