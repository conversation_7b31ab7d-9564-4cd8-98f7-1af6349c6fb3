<?php

namespace App\Traits\Attorney;

use Illuminate\Support\Facades\Auth;

trait UserRelatedID
{

    /**
     * @return array
     */
    private function getCountyIDs(): array
    {
        $data = Auth::user()->attorneyState()->with('county')->get()->pluck('county.*.id');
        return array_merge(...$data);
    }

    /**
     * @return object
     */
    private function getPracticeArea(): object
    {
        return Auth::user()->practiceArea()->pluck('practice_areas.id');
    }
}
