<?php


namespace App\Traits\ProfilePoints;

use App\Models\Attachment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;

trait ProfilePoints
{
    private function addProfilePoints()
    {

        $addPoint = 0;
        $profilePoint = Auth::user()->profile_points ?? 0;
        if(Request::method() != "PUT"){
            $addPoint = 1;
        }
        $point = tap(Auth::user())->update(['profile_points'=>($profilePoint+$addPoint)]);
        Session::put('profile_points',$point->profile_points);

    }

}
