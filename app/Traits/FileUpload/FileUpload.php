<?php


namespace App\Traits\FileUpload;

use App\Models\Attachment;
use Illuminate\Support\Facades\Storage;

trait FileUpload
{
    private function uploadToS3($file, string $directory, $filename = '')
    {
        $fileName = $filename ?: $file->hashName();
        return Storage::disk('s3')->put($directory . $fileName, file_get_contents($file)) ? $fileName : '';
    }

    private function getS3Url(string $directory, $image)
    {
        return $image ? config('filesystems.disks.s3-third.url') . $directory . $image : '';
    }

    private function uploadAttachment($file, string $filename, string $dir)
    {
        return (Attachment::create([
            'label' => sanitizeFileName($file),
            'file' => $filename,
            'mime_type' => $file->getClientmimeType(),
            'size' => $file->getSize(),
            'directory' => $dir,
            'extension' => pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION)
        ]))->id;
    }
}
