<?php

namespace App\Traits\UserDetailsUpdate;

use App\Traits\ProfilePoints\ProfilePoints;
use Illuminate\Http\Request;

trait UserInfoUpdate
{

    use ProfilePoints;
    /**
     * @param Request $request
     * @return $this
     */
    private function addPracticeArea(Request $request): self
    {
        $request->user()->practiceArea()->sync($request->practice_areas);
        $this->addProfilePoints();
        return $this;
    }

    /**
     * @param Request $request
     * @return $this
     */
    private function addStates(Request $request): self
    {
        $data = [];
        foreach ($request->state_details as $state) {
            $data[$state['state_id']] = ['bar_date' => $state['bar_date']];
        }
        $query = $request->user()->states();
        $query->sync($data);
        $this->addProfilePoints();
        return $this;
    }

    /**
     * @param Request $request
     */
    private function addCounties(Request $request): self
    {
        foreach ($request->state_details as $state) {
            $query = ($request->user()->attorneyState()->where('state_id', $state['state_id'])
                ->first()
                ->county());
            $query->detach();
            $query->attach($state['counties']);
        }
        $this->addProfilePoints();
        return $this;
    }

    /**
     * @param Request $request
     */
    private function addLanguage(Request $request): self
    {
        if ($request->has('language_id')) {
            $request->user()->userLanguage()->sync($request->language_id);
        }
        $this->addProfilePoints();
        return $this;
    }

}
