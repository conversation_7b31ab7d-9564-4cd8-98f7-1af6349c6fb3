<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;

class FirebaseNotification
{
    protected $messaging;

    public function __construct()
    {
        $factory = (new Factory)
            ->withServiceAccount(storage_path(env('FIREBASE_CREDENTIALS','app/google-services.json')));

        $this->messaging = $factory->createMessaging();
    }

    public function sendNotification($deviceToken, $title, $body, $data = [],$useNotificationOnly=false)
    {
        Log::info('DeviceToken:', [$deviceToken]);
        if (!$deviceToken) {
            return false;
        }
        $validatedData = $data;
        if ($useNotificationOnly) {
            $notification = CloudMessage::withTarget('token', $deviceToken)
                ->withNotification([
                    'title' => $title ?? "Legal Fiber",
                    'body' => $body,
                ])
                ->withNotification($validatedData);
            return $this->messaging->send($notification);
        } else {
            Log::debug($validatedData);
            $notification = CloudMessage::withTarget('token', $deviceToken)
                ->withNotification([
                    'title' => $title ?? "Legal Fiber",
                    'body' => $body,
                ])
                ->withData(
                    collect($validatedData)->map(function ($value) {
                        return is_scalar($value) ? (string) $value : json_encode($value);
                    })->toArray()
                );
            return $this->messaging->send($notification);
        }
    }
}
