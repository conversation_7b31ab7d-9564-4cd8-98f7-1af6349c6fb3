<?php

namespace App\Rules;

use App\Models\County;
use Illuminate\Contracts\Validation\Rule;

class StateCountyCheck implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
       foreach ($value as $val) {
           $checkedCounties = County::whereIn('id',$val['counties'])->where('state_id',$val['state_id'])->pluck('id')->toArray();
           if(!empty(array_diff($val['counties'],$checkedCounties))) {
            return false;
           }
       }
       return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Some states does not have counties.';
    }
}
