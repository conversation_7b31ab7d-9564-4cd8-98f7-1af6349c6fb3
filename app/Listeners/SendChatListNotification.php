<?php

namespace App\Listeners;

use App\Events\ChatListEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendChatListNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\ChatListEvent  $event
     * @return void
     */
    public function handle(ChatListEvent $event)
    {
        //
    }
}
