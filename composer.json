{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "doctrine/dbal": "^3.3", "fruitcake/laravel-cors": "^2.0", "google/apiclient": "^2.18", "grimzy/laravel-mysql-spatial": "4.0", "guzzlehttp/guzzle": "^7.0.1", "itsgoingd/clockwork": "^5.1", "kreait/laravel-firebase": "^4.2", "kutia-software-company/larafirebase": "^1.3", "laravel/cashier": "^13.9", "laravel/framework": "^8.75", "laravel/passport": "^10.3", "laravel/socialite": "^5.5", "laravel/tinker": "^2.5", "league/flysystem-aws-s3-v3": "^1.0", "predis/predis": "^2.2", "pusher/pusher-php-server": "^7.0", "socialiteproviders/apple": "^5.6", "twilio/sdk": "^6.35"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10", "rakutentech/laravel-request-docs": "^1.17"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}