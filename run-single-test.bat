@echo off
REM Windows batch script for running a single test file
REM Usage: run-single-test.bat path/to/test/file.php

REM Check if a test file was provided
IF "%~1"=="" (
    echo Error: No test file specified
    echo Usage: run-single-test.bat path/to/test/file.php
    exit /b 1
)

REM Set environment to testing
set APP_ENV=testing

REM Clear cache
echo Clearing cache...
php artisan config:clear
php artisan cache:clear

REM Prepare the test database
echo Preparing test database...

REM Create the testing database if it doesn't exist
echo Creating testing database if it doesn't exist...
mysql -u root -e "CREATE DATABASE IF NOT EXISTS legalfiber_testing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

REM Run migrations and seed the database
php artisan migrate:fresh --env=testing
php artisan db:seed --class="Database\Seeders\TestDatabaseSeeder" --env=testing

REM Run the specified test
echo.
echo === Running Test: %1 ===
echo.
php artisan test --env=testing %1

echo.
echo === Test Completed ===
echo.

pause
